#!/usr/bin/env python3
"""
Check if all required dependencies for web search fallback are installed
"""

def check_dependencies():
    """Check if all required dependencies are available"""
    
    print("🔍 Checking Web Search Fallback Dependencies")
    print("=" * 50)
    
    dependencies = [
        ("beautifulsoup4", "bs4"),
        ("requests", "requests"),
        ("aiohttp", "aiohttp"),
        ("lxml", "lxml"),
    ]
    
    missing_deps = []
    
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✅ {dep_name} - OK")
        except ImportError:
            print(f"❌ {dep_name} - MISSING")
            missing_deps.append(dep_name)
    
    print("\n" + "=" * 50)
    
    if missing_deps:
        print(f"❌ Missing dependencies: {', '.join(missing_deps)}")
        print("\nTo install missing dependencies, run:")
        print(f"pip install {' '.join(missing_deps)}")
        return False
    else:
        print("✅ All dependencies are installed!")
        
        # Test basic functionality
        print("\n🧪 Testing basic functionality...")
        
        try:
            from bs4 import BeautifulSoup
            import requests
            import aiohttp
            
            # Test BeautifulSoup
            soup = BeautifulSoup("<html><body><p>Test</p></body></html>", 'html.parser')
            assert soup.find('p').text == "Test"
            print("✅ BeautifulSoup - Working")
            
            # Test requests (basic)
            print("✅ Requests - Available")
            
            # Test aiohttp (basic)
            print("✅ aiohttp - Available")
            
            print("\n🎉 All dependencies are working correctly!")
            return True
            
        except Exception as e:
            print(f"❌ Dependency test failed: {str(e)}")
            return False

if __name__ == "__main__":
    success = check_dependencies()
    exit(0 if success else 1)
