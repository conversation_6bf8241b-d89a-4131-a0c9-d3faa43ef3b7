"""
Enhanced Web Search Fallback System
Provides intelligent web scraping and search capabilities when primary APIs fail
"""

import asyncio
import aiohttp
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

# Check for optional dependencies
try:
    from bs4 import BeautifulSoup
    import requests
    SCRAPING_AVAILABLE = True
except ImportError:
    SCRAPING_AVAILABLE = False
    BeautifulSoup = None
    requests = None

from ..config.logging_config import get_logger

class WebSearchFallback:
    """Enhanced web search fallback system with multi-level strategies"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Rate limiting
        self.last_request_time = {}
        self.min_request_interval = 2  # seconds between requests
        
        self.logger.info("WebSearchFallback initialized")
    
    async def search_financial_data(
        self,
        company_name: str,
        ticker: str,
        query_type: str,
        specific_request: str
    ) -> Dict[str, Any]:
        """
        Multi-level fallback search for financial data
        
        Levels:
        1. Enhanced web search tool (if available)
        2. Direct web scraping from financial sites
        3. Basic web search with data extraction
        4. Informative fallback message
        """
        
        self.logger.info(f"Starting web search fallback for {company_name} ({ticker})")
        
        # Level 1: Enhanced web search tool
        try:
            enhanced_result = await self._enhanced_web_search(company_name, ticker, query_type, specific_request)
            if enhanced_result:
                return enhanced_result
        except Exception as e:
            self.logger.warning(f"Enhanced web search not available: {str(e)}")
        
        # Level 2: Direct web scraping
        try:
            scraping_result = await self._direct_web_scraping(company_name, ticker, query_type, specific_request)
            if scraping_result:
                return scraping_result
        except Exception as e:
            self.logger.warning(f"Direct web scraping failed: {str(e)}")
        
        # Level 3: Basic web search fallback
        try:
            basic_result = await self._basic_web_search_fallback(company_name, ticker, query_type, specific_request)
            if basic_result:
                return basic_result
        except Exception as e:
            self.logger.warning(f"Basic web search failed: {str(e)}")
        
        # Level 4: Final fallback
        return self._final_fallback(company_name, ticker, query_type)
    
    async def _enhanced_web_search(
        self,
        company_name: str,
        ticker: str,
        query_type: str,
        specific_request: str
    ) -> Optional[Dict[str, Any]]:
        """Try enhanced web search tool if available"""
        try:
            from ..tools.web_search_tool import enhanced_web_search
            
            search_query = self._build_search_query(company_name, ticker, query_type, specific_request)
            
            result = await enhanced_web_search.search_and_analyze(
                query=search_query,
                max_results=5,
                include_analysis=True
            )
            
            if result.get('success'):
                formatted_result = self._format_enhanced_result(result, company_name, ticker, query_type)
                return {
                    "success": True,
                    "data": formatted_result,
                    "source": "enhanced_web_search",
                    "timestamp": datetime.utcnow().isoformat()
                }
            
            return None
            
        except ImportError:
            self.logger.debug("Enhanced web search tool not available")
            return None
        except Exception as e:
            self.logger.error(f"Enhanced web search error: {str(e)}")
            return None
    
    async def _direct_web_scraping(
        self,
        company_name: str,
        ticker: str,
        query_type: str,
        specific_request: str
    ) -> Optional[Dict[str, Any]]:
        """Direct web scraping without search APIs"""
        if not SCRAPING_AVAILABLE:
            self.logger.warning("Web scraping libraries not available - BeautifulSoup or requests not installed")
            return None

        try:
            # Build targeted URLs for financial data
            urls = self._build_financial_urls(company_name, ticker, query_type)
            self.logger.info(f"Attempting to scrape {len(urls)} URLs for {company_name} ({ticker})")

            scraped_data = []

            # Create connector with custom settings to handle header issues
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=2,
                ttl_dns_cache=300,
                use_dns_cache=True,
                ssl=False  # Disable SSL verification for problematic sites
            )

            # Use more realistic headers to avoid detection
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            timeout = aiohttp.ClientTimeout(total=30, connect=15)

            async with aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers=headers
            ) as session:
                for i, url in enumerate(urls[:3]):  # Limit to 3 URLs to avoid being blocked
                    try:
                        self.logger.info(f"Scraping URL {i+1}/{len(urls[:3])}: {url}")

                        # Add random delay to avoid rate limiting
                        if i > 0:
                            await asyncio.sleep(3 + (i * 2))  # Increasing delay

                        async with session.get(url, allow_redirects=True) as response:
                            self.logger.info(f"Response status for {url}: {response.status}")

                            if response.status == 200:
                                html = await response.text()
                                soup = BeautifulSoup(html, 'html.parser')

                                # Extract financial data
                                extracted = self._extract_from_scraped_content(
                                    soup, url, company_name, ticker, query_type
                                )
                                if extracted:
                                    scraped_data.append(extracted)
                                    self.logger.info(f"Successfully scraped data from {url}")
                                else:
                                    self.logger.warning(f"No data extracted from {url}")
                            elif response.status == 403:
                                self.logger.warning(f"Access forbidden (403) for {url} - likely blocked by anti-bot measures")
                            elif response.status == 429:
                                self.logger.warning(f"Rate limited (429) for {url} - too many requests")
                            else:
                                self.logger.warning(f"HTTP {response.status} for {url}")

                    except aiohttp.ClientError as e:
                        self.logger.warning(f"Client error scraping {url}: {str(e)}")
                        continue
                    except asyncio.TimeoutError:
                        self.logger.warning(f"Timeout scraping {url}")
                        continue
                    except Exception as e:
                        self.logger.warning(f"Failed to scrape {url}: {str(e)}")
                        continue

            self.logger.info(f"Scraping completed. Successfully scraped {len(scraped_data)} sources")

            if scraped_data:
                # Aggregate the scraped data
                aggregated = self._aggregate_scraped_data(scraped_data, company_name, ticker, query_type)
                return {
                    "success": True,
                    "data": aggregated,
                    "source": "direct_web_scraping",
                    "timestamp": datetime.utcnow().isoformat(),
                    "sources_scraped": len(scraped_data)
                }

            self.logger.warning(f"No data could be scraped for {company_name} ({ticker})")
            return None

        except Exception as e:
            self.logger.error(f"Direct web scraping error: {str(e)}")
            return None
    
    def _build_financial_urls(self, company_name: str, ticker: str, query_type: str) -> List[str]:
        """Build targeted URLs for financial data scraping with more reliable sources"""
        urls = []

        # Try different variations of the ticker
        ticker_variations = [ticker, ticker.upper(), ticker.lower()]

        # Add exchange suffixes for Indian stocks if not present
        if not any(suffix in ticker for suffix in ['.NS', '.BO', '.BSE']):
            ticker_variations.extend([f"{ticker}.NS", f"{ticker}.BO"])

        # Build URLs for each variation, prioritizing more scraping-friendly sources
        for tick in ticker_variations[:2]:  # Limit to avoid too many requests

            # MarketWatch (generally more accessible than Google Finance)
            urls.append(f"https://www.marketwatch.com/investing/stock/{tick}")

            # Seeking Alpha (has good financial data and less aggressive anti-bot)
            urls.append(f"https://seekingalpha.com/symbol/{tick}")

            # Yahoo Finance (try with different format)
            urls.append(f"https://finance.yahoo.com/quote/{tick}")

            # MSN Money (often overlooked but accessible)
            urls.append(f"https://www.msn.com/en-us/money/stockdetails/{tick}")

            # Google Finance (last resort due to heavy anti-bot measures)
            urls.append(f"https://www.google.com/finance/quote/{tick}")

        # Add company name-based searches for better coverage
        company_safe = company_name.replace(' ', '+').replace('&', 'and')
        urls.append(f"https://www.marketwatch.com/tools/quotes/lookup.asp?siteID=mktw&Lookup={company_safe}")

        return urls[:6]  # Limit to 6 URLs to avoid being blocked
    
    def _extract_from_scraped_content(
        self,
        soup: 'BeautifulSoup',
        url: str,
        company_name: str,
        ticker: str,
        query_type: str
    ) -> Optional[Dict[str, Any]]:
        """Extract financial data from scraped HTML content"""
        try:
            extracted = {
                "url": url,
                "title": "",
                "structured_data": {},
                "key_findings": []
            }

            # Get page title
            title_tag = soup.find('title')
            if title_tag:
                extracted["title"] = title_tag.get_text().strip()

            # Extract text content
            text_content = soup.get_text(separator=' ', strip=True)

            # Use existing extraction methods
            if query_type == "stock_price":
                extracted["structured_data"] = self._extract_price_data(text_content)
            elif query_type == "financial_statements":
                extracted["structured_data"] = self._extract_statement_data(text_content)
            elif query_type == "company_fundamentals":
                extracted["structured_data"] = self._extract_fundamental_data(text_content)

            # Extract key findings
            extracted["key_findings"] = self._extract_key_findings(text_content, company_name)

            return extracted if (extracted["structured_data"] or extracted["key_findings"]) else None

        except Exception as e:
            self.logger.error(f"Error extracting from scraped content: {str(e)}")
            return None
    
    def _extract_price_data(self, text: str) -> Dict[str, Any]:
        """Extract price data from text"""
        price_data = {}
        
        # Look for price patterns
        price_patterns = [
            r'[\$₹]?([\d,]+\.?\d*)',
            r'price[:\s]*([\d,]+\.?\d*)',
            r'current[:\s]*([\d,]+\.?\d*)'
        ]
        
        for pattern in price_patterns:
            matches = re.findall(pattern, text.lower())
            if matches:
                try:
                    price = float(matches[0].replace(',', ''))
                    if 0.01 <= price <= 1000000:  # Reasonable price range
                        price_data['current_price'] = price
                        break
                except ValueError:
                    continue
        
        return price_data
    
    def _extract_fundamental_data(self, text: str) -> Dict[str, Any]:
        """Extract fundamental data from text"""
        fundamental_data = {}
        
        # Look for market cap, P/E ratio, etc.
        patterns = {
            'market_cap': r'market cap[:\s]*[\$₹]?([\d,\.]+[bmk]?)',
            'pe_ratio': r'p/e[:\s]*([\d,\.]+)',
            'dividend_yield': r'dividend[:\s]*([\d,\.]+%?)'
        }
        
        for key, pattern in patterns.items():
            matches = re.findall(pattern, text.lower())
            if matches:
                fundamental_data[key] = matches[0]
        
        return fundamental_data
    
    def _extract_statement_data(self, text: str) -> Dict[str, Any]:
        """Extract financial statement data from text"""
        statement_data = {}
        
        # Look for revenue, profit, etc.
        patterns = {
            'revenue': r'revenue[:\s]*[\$₹]?([\d,\.]+[bmk]?)',
            'profit': r'profit[:\s]*[\$₹]?([\d,\.]+[bmk]?)',
            'earnings': r'earnings[:\s]*[\$₹]?([\d,\.]+[bmk]?)'
        }
        
        for key, pattern in patterns.items():
            matches = re.findall(pattern, text.lower())
            if matches:
                statement_data[key] = matches[0]
        
        return statement_data
    
    def _extract_key_findings(self, text: str, company_name: str) -> List[str]:
        """Extract key findings from text"""
        findings = []
        
        # Look for sentences containing the company name or financial keywords
        sentences = text.split('.')
        financial_keywords = ['price', 'revenue', 'profit', 'market', 'stock', 'shares', 'dividend']
        
        for sentence in sentences[:20]:  # Limit to first 20 sentences
            sentence = sentence.strip()
            if len(sentence) > 20 and len(sentence) < 200:  # Reasonable length
                if (company_name.lower() in sentence.lower() or 
                    any(keyword in sentence.lower() for keyword in financial_keywords)):
                    findings.append(sentence)
        
        return findings[:5]  # Return top 5 findings

    async def _basic_web_search_fallback(
        self,
        company_name: str,
        ticker: str,
        query_type: str,
        specific_request: str
    ) -> Dict[str, Any]:
        """Basic fallback using alternative search methods"""
        try:
            self.logger.info(f"Attempting basic web search fallback for {company_name} ({ticker})")

            # Use web search to get some real context
            search_query = self._build_search_query(company_name, ticker, query_type, specific_request)

            # Try to get some real web content using requests with alternative sources
            if not SCRAPING_AVAILABLE:
                self.logger.warning("Web scraping libraries not available for basic search")
                # Skip to final fallback
            else:
                # Try alternative search engines and financial aggregators
                search_sources = [
                    f"https://duckduckgo.com/html/?q={search_query.replace(' ', '+')}",
                    f"https://www.bing.com/search?q={search_query.replace(' ', '+')}",
                    # Try direct financial data sources
                    f"https://finance.yahoo.com/lookup?s={ticker}",
                    f"https://www.marketwatch.com/tools/quotes/lookup.asp?siteID=mktw&Lookup={ticker}",
                ]

                for search_url in search_sources[:2]:  # Try first 2 sources
                    try:
                        self.logger.info(f"Trying search source: {search_url}")

                        headers = {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Connection': 'keep-alive',
                        }

                        response = requests.get(search_url, headers=headers, timeout=10)
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            search_results = soup.get_text()

                            # Extract data from search results
                            extracted_data = self._extract_financial_data(
                                search_results, company_name, ticker, query_type
                            )

                            if extracted_data.get("structured_data") or extracted_data.get("key_findings"):
                                self.logger.info(f"Successfully extracted data from {search_url}")
                                return {
                                    "success": True,
                                    "data": extracted_data,
                                    "source": "basic_web_search",
                                    "timestamp": datetime.utcnow().isoformat(),
                                    "note": f"Data extracted from alternative search: {search_url}"
                                }
                        else:
                            self.logger.warning(f"HTTP {response.status_code} from {search_url}")

                    except Exception as e:
                        self.logger.warning(f"Basic web search failed for {search_url}: {str(e)}")
                        continue

            # If all search attempts fail, provide informative fallback
            self.logger.info(f"All search methods failed, providing informative fallback for {company_name} ({ticker})")
            return {
                "success": True,
                "data": {
                    "symbol": ticker,
                    "company_name": company_name,
                    "query_type": query_type,
                    "message": f"Unable to retrieve current {query_type} data for {company_name} ({ticker}). This may be due to API limitations, network issues, or the company not being publicly traded.",
                    "suggestions": [
                        "Please try again in a few moments",
                        "Verify the company ticker symbol is correct",
                        "Check if the company is publicly traded",
                        "Try searching for the company by its full name"
                    ],
                    "source": "web_search_fallback",
                    "attempted_methods": ["enhanced_web_search", "direct_web_scraping", "basic_web_search"]
                },
                "source": "web_search_fallback",
                "timestamp": datetime.utcnow().isoformat(),
                "note": "Fallback response when all data retrieval methods fail"
            }

        except Exception as e:
            self.logger.error(f"Basic fallback error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "source": "web_search_fallback",
                "timestamp": datetime.utcnow().isoformat()
            }

    def _final_fallback(self, company_name: str, ticker: str, query_type: str) -> Dict[str, Any]:
        """Final fallback when all other methods fail"""
        return {
            "success": True,
            "data": {
                "symbol": ticker,
                "company_name": company_name,
                "query_type": query_type,
                "message": f"I apologize, but I'm currently unable to retrieve {query_type} data for {company_name} ({ticker}). This could be due to temporary API limitations or network issues.",
                "suggestions": [
                    "Please try again in a few moments",
                    "Verify the company ticker symbol is correct",
                    "Check if the company is publicly traded",
                    "Try searching for the company by its full name"
                ],
                "source": "final_fallback"
            },
            "source": "final_fallback",
            "timestamp": datetime.utcnow().isoformat()
        }

    def _build_search_query(self, company_name: str, ticker: str, query_type: str, specific_request: str) -> str:
        """Build an optimized search query"""

        base_terms = [company_name, ticker]

        if query_type == "stock_price":
            query_terms = base_terms + ["stock price", "current price", "share price"]
        elif query_type == "company_fundamentals":
            query_terms = base_terms + ["fundamentals", "financial data", "market cap", "P/E ratio"]
        elif query_type == "financial_statements":
            query_terms = base_terms + ["financial statements", "earnings", "revenue", "income statement"]
        else:
            query_terms = base_terms + [specific_request]

        return " ".join(query_terms)

    def _extract_financial_data(self, text: str, company_name: str, ticker: str, query_type: str) -> Dict[str, Any]:
        """Extract financial data from search results text"""

        extracted = {
            "company_name": company_name,
            "ticker": ticker,
            "query_type": query_type,
            "structured_data": {},
            "key_findings": []
        }

        # Extract structured data based on query type
        if query_type == "stock_price":
            extracted["structured_data"] = self._extract_price_data(text)
        elif query_type == "company_fundamentals":
            extracted["structured_data"] = self._extract_fundamental_data(text)
        elif query_type == "financial_statements":
            extracted["structured_data"] = self._extract_statement_data(text)

        # Extract key findings
        extracted["key_findings"] = self._extract_key_findings(text, company_name)

        return extracted

    def _aggregate_scraped_data(
        self,
        scraped_data: List[Dict[str, Any]],
        company_name: str,
        ticker: str,
        query_type: str
    ) -> Dict[str, Any]:
        """Aggregate data from multiple scraped sources"""
        aggregated = {
            "company_name": company_name,
            "ticker": ticker,
            "query_type": query_type,
            "structured_data": {},
            "key_findings": [],
            "sources": []
        }

        # Combine structured data
        for data in scraped_data:
            if data.get("structured_data"):
                for key, value in data["structured_data"].items():
                    if key not in aggregated["structured_data"]:
                        aggregated["structured_data"][key] = value

            # Combine key findings
            if data.get("key_findings"):
                aggregated["key_findings"].extend(data["key_findings"])

            # Track sources
            if data.get("url"):
                aggregated["sources"].append({
                    "url": data["url"],
                    "title": data.get("title", "")
                })

        # Remove duplicate findings
        aggregated["key_findings"] = list(set(aggregated["key_findings"]))[:5]

        return aggregated

    def _format_enhanced_result(self, enhanced_result: Dict[str, Any], company_name: str, ticker: str, query_type: str) -> Dict[str, Any]:
        """Format enhanced search result for compatibility with existing system"""

        try:
            formatted_data = {
                "company_name": company_name,
                "ticker": ticker,
                "query_type": query_type,
                "data_quality_score": enhanced_result.get('data_quality_score', 0.0),
                "sources_used": enhanced_result.get('sources_used', 0),
                "successful_extractions": enhanced_result.get('successful_extractions', 0)
            }

            # Add financial metrics
            financial_metrics = enhanced_result.get('financial_metrics', {})
            if financial_metrics:
                formatted_data["structured_data"] = financial_metrics

            # Add key insights
            key_insights = enhanced_result.get('key_insights', [])
            if key_insights:
                formatted_data["key_findings"] = key_insights

            # Add LLM analysis if available
            llm_analysis = enhanced_result.get('llm_analysis', {})
            if llm_analysis:
                formatted_data["llm_analysis"] = llm_analysis.get('analysis', '')
                formatted_data["llm_findings"] = llm_analysis.get('key_findings', [])
                formatted_data["llm_recommendations"] = llm_analysis.get('recommendations', [])
                formatted_data["llm_confidence"] = llm_analysis.get('confidence_score', 0.0)

            # Add metadata
            formatted_data["metadata"] = {
                "enhanced_search": True,
                "processing_time_ms": enhanced_result.get('processing_time_ms', 0),
                "from_cache": enhanced_result.get('from_cache', False)
            }

            return formatted_data

        except Exception as e:
            self.logger.error(f"Error formatting enhanced result: {str(e)}")
            return {
                "company_name": company_name,
                "ticker": ticker,
                "query_type": query_type,
                "error": "Failed to format enhanced result",
                "raw_data": enhanced_result
            }

    async def find_missing_ticker(self, company_name: str) -> Optional[str]:
        """Find ticker for a company name using web search"""
        try:
            search_query = f"{company_name} stock ticker symbol"

            if not SCRAPING_AVAILABLE:
                return None

            search_url = f"https://www.google.com/search?q={search_query.replace(' ', '+')}"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(search_url, headers=headers, timeout=5)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                text = soup.get_text()

                # Look for ticker patterns
                ticker_patterns = [
                    r'\b([A-Z]{1,5})\b.*stock',
                    r'ticker[:\s]*([A-Z]{1,5})',
                    r'symbol[:\s]*([A-Z]{1,5})'
                ]

                for pattern in ticker_patterns:
                    matches = re.findall(pattern, text)
                    if matches:
                        return matches[0]

            return None

        except Exception as e:
            self.logger.error(f"Error finding ticker: {str(e)}")
            return None


# Global instance
web_search_fallback = WebSearchFallback()
