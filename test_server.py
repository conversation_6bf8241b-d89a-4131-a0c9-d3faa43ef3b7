#!/usr/bin/env python3
"""
Quick test script to verify the server is working
"""

import requests
import time

def test_server():
    """Test the server endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Financial Analyst Chatbot Server")
    print("=" * 50)
    
    # Test 1: Health check
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check: PASSED")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check: FAILED (HTTP {response.status_code})")
    except Exception as e:
        print(f"❌ Health check: FAILED ({str(e)})")
    
    # Test 2: Main page
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✅ Main page: PASSED")
        else:
            print(f"❌ Main page: FAILED (HTTP {response.status_code})")
    except Exception as e:
        print(f"❌ Main page: FAILED ({str(e)})")
    
    # Test 3: Chat API
    try:
        chat_data = {"message": "What is Apple stock price?"}
        response = requests.post(f"{base_url}/api/chat", json=chat_data, timeout=10)
        if response.status_code == 200:
            print("✅ Chat API: PASSED")
            result = response.json()
            print(f"   Response: {result.get('response', 'No response')[:100]}...")
            print(f"   Sources: {result.get('sources', [])}")
        else:
            print(f"❌ Chat API: FAILED (HTTP {response.status_code})")
    except Exception as e:
        print(f"❌ Chat API: FAILED ({str(e)})")
    
    print("\n🎯 Test Summary:")
    print("If all tests passed, your chatbot is working correctly!")
    print("Visit http://localhost:8000 to use the web interface")

if __name__ == "__main__":
    test_server()
