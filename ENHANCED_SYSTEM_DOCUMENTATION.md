# Enhanced LLM Query Understanding System

## Overview

This enhanced system provides sophisticated query understanding, intelligent ticker discovery and storage, specific error handling, and seamless web search integration for financial data retrieval.

## Key Components

### 1. 🧠 Query Understanding Engine (`src/query_processing/query_understanding_engine.py`)

**Purpose**: Advanced LLM-powered query analysis using OpenAI function calling

**Features**:
- Sophisticated intent classification (8 specific intents)
- Company entity extraction with confidence scoring
- Parameter extraction (time periods, metrics, date ranges)
- API call suggestions based on understanding
- Alternative interpretation handling
- Error recovery suggestions

**Usage**:
```python
from src.query_processing.query_understanding_engine import QueryUnderstandingEngine

engine = QueryUnderstandingEngine()
understanding = await engine.understand_query("What's Apple's stock price?")

print(f"Intent: {understanding.intent.value}")
print(f"Companies: {[c.name for c in understanding.companies]}")
print(f"Suggested APIs: {understanding.suggested_api_calls}")
```

### 2. 💾 Ticker Mapping Manager (`src/utils/ticker_mapping_manager.py`)

**Purpose**: Persistent storage and intelligent retrieval of company-ticker mappings

**Features**:
- JSON-based persistent storage
- Fuzzy matching with 80% similarity threshold
- Built-in mappings for 50+ major companies
- Company name normalization and cleaning
- Search functionality with confidence scoring
- Metadata tracking (discovery date, source, confidence)

**Usage**:
```python
from src.utils.ticker_mapping_manager import ticker_mapping_manager

# Find existing ticker
ticker = ticker_mapping_manager.find_ticker("Apple Inc")

# Add new mapping
await ticker_mapping_manager.add_mapping(
    company_name="New Company",
    ticker="NEWCO",
    confidence=0.9,
    source="web_discovery"
)

# Search companies
results = ticker_mapping_manager.search_companies("apple", limit=5)
```

### 3. ⚠️ Error Handling System (`src/utils/error_handling.py`)

**Purpose**: Specific error categorization with actionable user messages

**Features**:
- 14 specific error categories
- Severity levels (Low, Medium, High, Critical)
- Context-aware error messages
- Specific recovery suggestions
- Retry possibility indication
- Estimated fix time

**Error Categories**:
- `INVALID_TICKER` - Wrong or non-existent ticker symbols
- `COMPANY_NOT_FOUND` - Company name not mappable to ticker
- `API_RATE_LIMIT` - API quota exceeded
- `API_KEY_INVALID` - Authentication issues
- `NETWORK_ERROR` - Connection problems
- `DATA_NOT_AVAILABLE` - Requested data doesn't exist
- And 8 more specific categories...

**Usage**:
```python
from src.utils.error_handling import error_handler, ErrorCategory

try:
    # Some operation that might fail
    pass
except Exception as e:
    details = error_handler.handle_error(e, context={"symbol": "INVALID"})
    print(f"Error: {details.user_message}")
    print(f"Suggestions: {details.suggestions}")
```

### 4. 🌐 Enhanced Web Search Integration (`src/utils/web_search_fallback.py`)

**Purpose**: Intelligent ticker discovery and financial data retrieval

**Features**:
- Multi-source ticker discovery (financial sites, search engines)
- Automatic ticker storage after discovery
- Enhanced search patterns and validation
- Name variation handling
- False positive filtering

**Enhanced Methods**:
- `find_missing_ticker()` - Now stores discovered tickers automatically
- `_search_ticker_multiple_sources()` - Uses 3 different strategies
- `_validate_ticker()` - Filters out common false positives

### 5. 🔧 Enhanced Query Parser (`src/query_processing/parser.py`)

**Purpose**: Improved query parsing with ticker mapping integration

**Features**:
- Integration with ticker mapping manager
- Enhanced company name extraction
- Multi-word company name detection
- Automatic ticker discovery for unknown companies
- Fallback to web search when needed

## System Flow

```
User Query
    ↓
Query Understanding Engine (LLM Analysis)
    ↓
Enhanced Query Parser
    ↓
Ticker Mapping Manager (Check existing mappings)
    ↓
Web Search Fallback (If ticker not found)
    ↓
Data Source Manager (API calls with error handling)
    ↓
Specific Error Handling (If APIs fail)
    ↓
Structured Response
```

## Key Improvements

### 1. **LLM Query Understanding**
- **Before**: Basic pattern matching with limited OpenAI integration
- **After**: Sophisticated function calling with structured output, intent classification, and API parameter generation

### 2. **Ticker Discovery & Storage**
- **Before**: Hardcoded mappings, no persistence, repeated lookups
- **After**: Persistent storage, fuzzy matching, automatic discovery and storage, 50+ built-in mappings

### 3. **Error Handling**
- **Before**: Generic "Unable to retrieve data" messages
- **After**: 14 specific error categories with actionable suggestions, severity levels, and retry guidance

### 4. **Web Search Integration**
- **Before**: Basic Google search scraping
- **After**: Multi-source strategy, ticker validation, automatic storage, enhanced patterns

## Configuration

### Environment Variables
```bash
# Required for enhanced query understanding
OPENAI_API_KEY=your_openai_key

# Optional API keys for data sources
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key

# Data storage directory
DATA_DIR=data
```

### File Structure
```
data/
├── ticker_mappings.json    # Persistent ticker storage
└── logs/                   # Application logs

src/
├── query_processing/
│   ├── query_understanding_engine.py
│   └── parser.py (enhanced)
├── utils/
│   ├── ticker_mapping_manager.py
│   ├── error_handling.py
│   ├── web_search_fallback.py (enhanced)
│   └── data_source_manager.py (enhanced)
└── config/
    └── settings.py
```

## Testing

Run the comprehensive test suite:
```bash
python test_enhanced_system.py
```

This tests:
- Ticker mapping functionality
- Query understanding engine
- Web search with ticker discovery
- Error handling system
- Enhanced query parser
- Data source manager integration

## Example Interactions

### Query: "What's Spotify's current stock price?"

**System Processing**:
1. **Query Understanding**: Intent=GET_STOCK_PRICE, Company="Spotify", Confidence=0.9
2. **Ticker Mapping**: Check for "Spotify" → Not found
3. **Web Search**: Discover "Spotify" → "SPOT"
4. **Storage**: Store mapping "Spotify" → "SPOT" 
5. **API Call**: get_stock_price("SPOT")
6. **Response**: Current price with source attribution

### Query: "Show me financial data for INVALIDTICKER"

**System Processing**:
1. **Query Understanding**: Intent=GET_COMPANY_INFO, Ticker="INVALIDTICKER"
2. **API Call**: get_company_fundamentals("INVALIDTICKER")
3. **Error Handling**: Categorize as INVALID_TICKER
4. **Response**: 
   - "The stock ticker 'INVALIDTICKER' was not found."
   - Suggestions: Check spelling, use company name, verify public trading
   - Retry possible: Yes, Estimated fix: Immediate

## Benefits

1. **Improved Accuracy**: LLM understanding reduces misinterpretation
2. **Reduced Latency**: Persistent ticker storage eliminates repeated lookups
3. **Better UX**: Specific error messages with actionable suggestions
4. **Scalability**: Automatic ticker discovery expands coverage
5. **Maintainability**: Modular design with clear separation of concerns

## Future Enhancements

1. **Machine Learning**: Train models on query patterns for better understanding
2. **Real-time Updates**: Sync ticker mappings with financial databases
3. **Multi-language**: Support for non-English company names
4. **Advanced Analytics**: Track query success rates and optimize accordingly
5. **API Integration**: Direct integration with more financial data providers
