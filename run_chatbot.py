#!/usr/bin/env python3
"""
Financial Analyst Chatbot - Main Application
A comprehensive financial data analysis chatbot with web search fallback
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
import sys
import os
from pathlib import Path
from datetime import datetime
import traceback

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

# Import our modules
try:
    from src.config.logging_config import setup_logging, get_logger
    from src.config.settings import get_settings
    from src.query_processing.parser import QueryParser
    from src.query_processing.executor import QueryExecutor, ExecutionResult
    from src.utils.data_source_manager import DataSourceManager
    from src.utils.response_formatter import ResponseFormatter
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"Import error: {e}")
    print("Some modules may be missing. Will create them...")
    IMPORTS_SUCCESSFUL = False

    # Create minimal fallback functions
    def setup_logging():
        import logging
        logging.basicConfig(level=logging.INFO)

    def get_logger(name):
        import logging
        return logging.getLogger(name)

# Initialize FastAPI app
app = FastAPI(
    title="Financial Analyst Chatbot",
    description="AI-powered financial data analysis with web search fallback",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Request/Response models
class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    sources: list = []
    execution_time_ms: float = 0
    timestamp: str = ""

# Global variables
logger = None
query_parser = None
query_executor = None
data_manager = None
response_formatter = None

async def initialize_components():
    """Initialize all components"""
    global logger, query_parser, query_executor, data_manager, response_formatter

    try:
        # Setup logging
        setup_logging()
        logger = get_logger(__name__)
        logger.info("Starting Financial Analyst Chatbot...")

        if IMPORTS_SUCCESSFUL:
            # Initialize components
            data_manager = DataSourceManager()
            query_parser = QueryParser()
            query_executor = QueryExecutor(data_manager)
            response_formatter = ResponseFormatter()

            logger.info("All components initialized successfully")
        else:
            logger.warning("Some imports failed - running in limited mode")
            # Create minimal fallback components
            data_manager = None
            query_parser = None
            query_executor = None
            response_formatter = None

        return True

    except Exception as e:
        print(f"Failed to initialize components: {e}")
        traceback.print_exc()
        return False

@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    success = await initialize_components()
    if not success:
        print("Failed to initialize. Some features may not work properly.")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main chat interface"""
    try:
        with open("static/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <head><title>Financial Chatbot</title></head>
            <body>
                <h1>Financial Analyst Chatbot</h1>
                <p>Static files are being restored. Please wait...</p>
                <script>setTimeout(() => location.reload(), 3000);</script>
            </body>
        </html>
        """)

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint"""
    start_time = datetime.now()
    
    try:
        if not all([query_parser, query_executor, response_formatter]):
            # Try to initialize if not already done
            await initialize_components()
            
        if not all([query_parser, query_executor, response_formatter]):
            return ChatResponse(
                response="System is initializing. Please try again in a moment.",
                sources=["System"],
                execution_time_ms=0,
                timestamp=datetime.now().isoformat()
            )
        
        # Parse the query
        parsed_query = await query_parser.parse_query(request.message)
        
        # Execute the query
        result = await query_executor.execute_query(parsed_query)
        
        # Format the response
        formatted_response = await response_formatter.format_response(result, parsed_query)
        
        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return ChatResponse(
            response=formatted_response,
            sources=result.sources_used if hasattr(result, 'sources_used') else ["Financial APIs"],
            execution_time_ms=execution_time,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        logger.error(traceback.format_exc())
        
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return ChatResponse(
            response=f"I apologize, but I encountered an error while processing your request: {str(e)}. Please try again or rephrase your question.",
            sources=["Error Handler"],
            execution_time_ms=execution_time,
            timestamp=datetime.now().isoformat()
        )

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "query_parser": query_parser is not None,
            "query_executor": query_executor is not None,
            "data_manager": data_manager is not None,
            "response_formatter": response_formatter is not None
        }
    }

@app.get("/api/status")
async def get_status():
    """Get system status"""
    try:
        if data_manager:
            status = await data_manager.get_system_status()
            return status
        else:
            return {
                "status": "initializing",
                "message": "System is still initializing"
            }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

def main():
    """Main function to run the application"""
    print("🚀 Starting Financial Analyst Chatbot")
    print("=" * 50)
    
    # Check if required directories exist
    required_dirs = ["static", "src", "logs"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            print(f"Created directory: {dir_name}")
    
    # Run the server
    try:
        uvicorn.run(
            "run_chatbot:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down Financial Analyst Chatbot")
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
