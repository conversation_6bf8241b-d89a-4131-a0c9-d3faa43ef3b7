# 🚀 Financial Analyst Chatbot

A comprehensive AI-powered financial data analysis chatbot with intelligent web search fallback capabilities.

## ✨ Features

- **🤖 AI-Powered Query Processing**: Natural language understanding for financial queries
- **📊 Multi-Source Data Integration**: Alpha Vantage, Finnhub, Yahoo Finance APIs
- **🌐 Web Search Fallback**: Intelligent web scraping when primary APIs fail
- **⚡ Real-Time Data**: Live stock prices, company fundamentals, financial statements
- **🎨 Modern Web Interface**: Dark theme, responsive design, real-time chat
- **🔄 Automatic Failover**: Seamless fallback between data sources
- **📈 Company Analysis**: Comprehensive financial metrics and comparisons

## 🚀 Quick Start

### Option 1: Simple Start (Recommended)
```bash
# Install dependencies
pip install -r requirements.txt

# Start the server (creates .env automatically)
python start_server.py
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Copy environment template
cp .env.example .env

# Edit .env with your API keys (optional)
# nano .env

# Start the chatbot
python run_chatbot.py
```

## 🌐 Access the Chatbot

Once started, access the chatbot at:
- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/api/health

## 💡 Example Queries

Try these example queries:
- "What's Apple's stock price?"
- "Compare Tesla vs Ford"
- "Microsoft company fundamentals"
- "SIYSIL stock price" (Indian stock)
- "What is the P/E ratio of Google?"

## 🔧 Configuration

### API Keys (Optional but Recommended)

Add these to your `.env` file for enhanced functionality:

```env
# OpenAI (for advanced query understanding)
OPENAI_API_KEY=your_openai_api_key

# Financial Data APIs
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key
```

### Free API Keys

Get free API keys from:
- **Alpha Vantage**: https://www.alphavantage.co/support/#api-key
- **Finnhub**: https://finnhub.io/register
- **OpenAI**: https://platform.openai.com/api-keys

## 🌟 Key Capabilities

### 1. **Smart Fallback System**
- Primary APIs → Web Search → Informative Responses
- Never shows generic error messages
- Always provides useful information

### 2. **Multi-Market Support**
- US Stocks (AAPL, MSFT, GOOGL, etc.)
- Indian Stocks (RELIANCE.NS, TCS.NS, etc.)
- International Markets

### 3. **Comprehensive Data**
- Real-time stock prices
- Company fundamentals
- Financial ratios
- Market analysis

### 4. **Intelligent Query Processing**
- Natural language understanding
- Entity extraction
- Intent classification
- Context awareness

## 📁 Project Structure

```
financial-chatbot/
├── src/
│   ├── config/           # Configuration and settings
│   ├── query_processing/ # Query parsing and execution
│   └── utils/           # Data sources and utilities
├── static/              # Web interface files
├── tests/               # Test files
├── logs/                # Application logs
├── run_chatbot.py       # Main application
├── start_server.py      # Quick start script
└── requirements.txt     # Dependencies
```

## 🧪 Testing

The chatbot includes comprehensive tests:

```bash
# Run integration tests
cd tests/integration
python test_comprehensive_final.py

# Run web search tests
python test_web_search_standalone.py
```

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Run `pip install -r requirements.txt`
2. **Port Already in Use**: Change port in `run_chatbot.py`
3. **API Rate Limits**: The system automatically falls back to web search
4. **No Data Found**: Try different company names or stock symbols

### Debug Mode

Enable debug mode in `.env`:
```env
DEBUG=true
LOG_LEVEL=DEBUG
```

## 🌐 Web Search Fallback

The chatbot features an advanced web search fallback system that:
- Scrapes financial data from Google Finance, MarketWatch, Yahoo Finance
- Extracts structured data from web pages
- Provides intelligent responses even when APIs fail
- Supports both US and international stocks

## 📊 Data Sources

1. **Primary APIs**:
   - Alpha Vantage (comprehensive financial data)
   - Finnhub (real-time market data)
   - Yahoo Finance (broad market coverage)

2. **Fallback Sources**:
   - Google Finance (web scraping)
   - MarketWatch (web scraping)
   - Financial news sites

## 🚀 Deployment

### Local Development
```bash
python start_server.py
```

### Production Deployment
```bash
# Using uvicorn directly
uvicorn run_chatbot:app --host 0.0.0.0 --port 8000

# Using gunicorn
gunicorn run_chatbot:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

If you encounter any issues:
1. Check the logs in the `logs/` directory
2. Verify your API keys in `.env`
3. Try the web search fallback functionality
4. Check the troubleshooting section above

---

**🎉 Enjoy using your Financial Analyst Chatbot!**
