"""
Response Formatter
Formats execution results into user-friendly responses
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from ..config.logging_config import get_logger
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..query_processing.parser import Parsed<PERSON><PERSON>y, QueryType
    from ..query_processing.executor import ExecutionResult

class ResponseFormatter:
    """Formats execution results into user-friendly responses"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    async def format_response(self, result, parsed_query) -> str:
        """Format execution result into a user-friendly response"""
        
        try:
            if not result.success:
                return self._format_error_response(result, parsed_query)
            
            query_type_value = getattr(parsed_query.query_type, 'value', str(parsed_query.query_type))

            if query_type_value == "stock_price":
                return self._format_stock_price_response(result, parsed_query)
            elif query_type_value == "company_fundamentals":
                return self._format_fundamentals_response(result, parsed_query)
            elif query_type_value == "financial_statements":
                return self._format_statements_response(result, parsed_query)
            elif query_type_value == "comparison":
                return self._format_comparison_response(result, parsed_query)
            elif query_type_value == "trend_analysis":
                return self._format_trend_response(result, parsed_query)
            else:
                return self._format_general_response(result, parsed_query)
                
        except Exception as e:
            self.logger.error(f"Error formatting response: {str(e)}")
            return f"I apologize, but I encountered an error while formatting the response: {str(e)}"
    
    def _format_error_response(self, result, parsed_query) -> str:
        """Format error response"""
        
        if result.errors:
            error_msg = result.errors[0]
            
            # Check if it's a web search fallback response
            if "web search" in error_msg.lower() or "fallback" in error_msg.lower():
                return ("I encountered some issues with the primary financial data sources, "
                       "but I was able to find some information through web search. "
                       "The data may be less comprehensive than usual.")
            
            return (f"I apologize, but I encountered an issue retrieving the requested financial data: {error_msg}. "
                   f"Please try again or rephrase your question.")
        
        return "I apologize, but I was unable to retrieve the requested financial data at this time. Please try again later."
    
    def _format_stock_price_response(self, result, parsed_query) -> str:
        """Format stock price response"""

        responses = []

        for description, data in result.data.items():
            if isinstance(data, dict):
                symbol = data.get('symbol', 'Unknown')
                company_name = data.get('company_name', symbol)
                
                # Handle different data formats
                if 'price' in data:
                    price = data['price']
                    change = data.get('change', 0)
                    change_percent = data.get('change_percent', '0%')
                    
                    response = f"**{company_name} ({symbol})**\n"
                    response += f"💰 Current Price: ${price:.2f}\n"
                    
                    if change != 0:
                        direction = "📈" if change > 0 else "📉"
                        response += f"{direction} Change: ${change:.2f} ({change_percent})\n"
                    
                    if 'volume' in data and data['volume']:
                        response += f"📊 Volume: {data['volume']:,}\n"
                    
                    responses.append(response)
                
                elif 'message' in data:
                    # Web search fallback response
                    response = f"**{company_name} ({symbol})**\n"
                    response += f"ℹ️ {data['message']}\n"
                    
                    if 'structured_data' in data and data['structured_data']:
                        structured = data['structured_data']
                        if 'current_price' in structured:
                            response += f"💰 Price: ${structured['current_price']:.2f}\n"
                    
                    if 'key_findings' in data and data['key_findings']:
                        response += "\n📋 Key Information:\n"
                        for finding in data['key_findings'][:3]:
                            response += f"• {finding}\n"
                    
                    responses.append(response)
        
        if responses:
            final_response = "\n".join(responses)
            
            # Add source information
            if result.sources_used:
                final_response += f"\n\n📊 Data Sources: {', '.join(result.sources_used)}"
            
            return final_response
        
        return "I couldn't retrieve stock price information for the requested symbols."
    
    def _format_fundamentals_response(self, result: ExecutionResult, parsed_query: ParsedQuery) -> str:
        """Format company fundamentals response"""
        
        responses = []
        
        for description, data in result.data.items():
            if isinstance(data, dict):
                symbol = data.get('symbol', 'Unknown')
                company_name = data.get('company_name', symbol)
                
                response = f"**{company_name} ({symbol}) - Company Fundamentals**\n\n"
                
                # Handle different data formats
                if 'market_cap' in data and data['market_cap']:
                    response += f"🏢 Market Cap: {data['market_cap']}\n"
                
                if 'pe_ratio' in data and data['pe_ratio']:
                    response += f"📊 P/E Ratio: {data['pe_ratio']}\n"
                
                if 'dividend_yield' in data and data['dividend_yield']:
                    response += f"💰 Dividend Yield: {data['dividend_yield']}\n"
                
                if 'eps' in data and data['eps']:
                    response += f"📈 EPS: {data['eps']}\n"
                
                if 'revenue' in data and data['revenue']:
                    response += f"💵 Revenue: {data['revenue']}\n"
                
                if 'sector' in data and data['sector']:
                    response += f"🏭 Sector: {data['sector']}\n"
                
                if 'industry' in data and data['industry']:
                    response += f"🔧 Industry: {data['industry']}\n"
                
                # Handle web search fallback data
                if 'message' in data:
                    response += f"\nℹ️ {data['message']}\n"
                
                if 'key_findings' in data and data['key_findings']:
                    response += "\n📋 Key Information:\n"
                    for finding in data['key_findings'][:5]:
                        response += f"• {finding}\n"
                
                responses.append(response)
        
        if responses:
            final_response = "\n".join(responses)
            
            # Add source information
            if result.sources_used:
                final_response += f"\n\n📊 Data Sources: {', '.join(result.sources_used)}"
            
            return final_response
        
        return "I couldn't retrieve fundamental information for the requested companies."
    
    def _format_statements_response(self, result: ExecutionResult, parsed_query: ParsedQuery) -> str:
        """Format financial statements response"""
        
        responses = []
        
        for description, data in result.data.items():
            if isinstance(data, dict):
                symbol = data.get('symbol', 'Unknown')
                company_name = data.get('company_name', symbol)
                
                response = f"**{company_name} ({symbol}) - Financial Statements**\n\n"
                
                if 'revenue' in data and data['revenue']:
                    response += f"💵 Revenue: {data['revenue']}\n"
                
                if 'profit' in data and data['profit']:
                    response += f"💰 Profit: {data['profit']}\n"
                
                if 'earnings' in data and data['earnings']:
                    response += f"📈 Earnings: {data['earnings']}\n"
                
                # Handle web search fallback data
                if 'message' in data:
                    response += f"\nℹ️ {data['message']}\n"
                
                responses.append(response)
        
        if responses:
            final_response = "\n".join(responses)
            
            # Add source information
            if result.sources_used:
                final_response += f"\n\n📊 Data Sources: {', '.join(result.sources_used)}"
            
            return final_response
        
        return "I couldn't retrieve financial statement information for the requested companies."
    
    def _format_comparison_response(self, result: ExecutionResult, parsed_query: ParsedQuery) -> str:
        """Format comparison response"""
        
        if len(result.data) < 2:
            return "I need at least two companies to make a comparison. Please provide more company symbols."
        
        response = "**Company Comparison**\n\n"
        
        companies = []
        for description, data in result.data.items():
            if isinstance(data, dict):
                symbol = data.get('symbol', 'Unknown')
                company_name = data.get('company_name', symbol)
                companies.append({
                    'name': company_name,
                    'symbol': symbol,
                    'data': data
                })
        
        # Compare key metrics
        response += "📊 **Key Metrics Comparison:**\n\n"
        
        for company in companies:
            response += f"**{company['name']} ({company['symbol']})**\n"
            
            data = company['data']
            if 'price' in data:
                response += f"💰 Price: ${data['price']:.2f}\n"
            if 'market_cap' in data and data['market_cap']:
                response += f"🏢 Market Cap: {data['market_cap']}\n"
            if 'pe_ratio' in data and data['pe_ratio']:
                response += f"📊 P/E Ratio: {data['pe_ratio']}\n"
            
            response += "\n"
        
        # Add source information
        if result.sources_used:
            response += f"📊 Data Sources: {', '.join(result.sources_used)}"
        
        return response
    
    def _format_trend_response(self, result: ExecutionResult, parsed_query: ParsedQuery) -> str:
        """Format trend analysis response"""
        
        response = "**Trend Analysis**\n\n"
        
        for description, data in result.data.items():
            if isinstance(data, dict):
                symbol = data.get('symbol', 'Unknown')
                company_name = data.get('company_name', symbol)
                
                response += f"**{company_name} ({symbol})**\n"
                response += "📈 Historical trend analysis is available through our data sources.\n"
                
                if 'price' in data:
                    response += f"💰 Current Price: ${data['price']:.2f}\n"
                
                response += "\n"
        
        # Add source information
        if result.sources_used:
            response += f"📊 Data Sources: {', '.join(result.sources_used)}"
        
        return response
    
    def _format_general_response(self, result: ExecutionResult, parsed_query: ParsedQuery) -> str:
        """Format general response"""
        
        if not result.data:
            return "I couldn't find specific information for your query. Please try being more specific about what financial data you're looking for."
        
        response = "**Financial Information**\n\n"
        
        for description, data in result.data.items():
            if isinstance(data, dict):
                symbol = data.get('symbol', 'Unknown')
                company_name = data.get('company_name', symbol)
                
                response += f"**{company_name} ({symbol})**\n"
                
                # Show available data
                if 'price' in data:
                    response += f"💰 Price: ${data['price']:.2f}\n"
                
                if 'market_cap' in data and data['market_cap']:
                    response += f"🏢 Market Cap: {data['market_cap']}\n"
                
                # Handle web search fallback data
                if 'message' in data:
                    response += f"ℹ️ {data['message']}\n"
                
                if 'key_findings' in data and data['key_findings']:
                    response += "\n📋 Key Information:\n"
                    for finding in data['key_findings'][:3]:
                        response += f"• {finding}\n"
                
                response += "\n"
        
        # Add source information
        if result.sources_used:
            response += f"📊 Data Sources: {', '.join(result.sources_used)}"
        
        return response
