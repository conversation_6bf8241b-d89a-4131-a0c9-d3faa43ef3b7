/* Dark Theme Financial Chatbot Styles */
:root {
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #808080;
    --accent-primary: #00d4aa;
    --accent-secondary: #0099cc;
    --accent-danger: #ff4757;
    --accent-warning: #ffa502;
    --border-color: #333333;
    --shadow-light: rgba(0, 212, 170, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px var(--shadow-dark);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 1.5rem;
    color: var(--accent-primary);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-danger);
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: var(--accent-primary);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

/* Message Styles */
.message {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
}

.user-message .message-avatar {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.user-message .message-content {
    text-align: right;
}

.message-text {
    background: var(--bg-secondary);
    padding: 1rem 1.25rem;
    border-radius: 1rem;
    border: 1px solid var(--border-color);
    line-height: 1.6;
}

.user-message .message-text {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
}

.message-text p {
    margin-bottom: 0.5rem;
}

.message-text p:last-child {
    margin-bottom: 0;
}

.message-text ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.message-text li {
    margin-bottom: 0.25rem;
}

.message-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.user-message .message-meta {
    justify-content: flex-end;
}

/* Input Container */
.input-container {
    padding: 1.5rem 2rem 2rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.input-wrapper {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

#messageInput {
    flex: 1;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 0.875rem 1.25rem;
    color: var(--text-primary);
    font-size: 0.95rem;
    outline: none;
    transition: all 0.2s ease;
}

#messageInput:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px var(--shadow-light);
}

#messageInput::placeholder {
    color: var(--text-muted);
}

.send-button {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px var(--shadow-light);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Suggestion Chips */
.input-suggestions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.suggestion-chip {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-chip:hover {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        padding: 1rem;
    }
    
    .chat-messages {
        padding: 1rem;
    }
    
    .input-container {
        padding: 1rem;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .logo h1 {
        font-size: 1.25rem;
    }
    
    .input-suggestions {
        display: none;
    }
}
