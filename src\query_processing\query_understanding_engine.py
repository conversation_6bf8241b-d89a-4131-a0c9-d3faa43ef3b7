"""
Enhanced LLM Query Understanding Engine
Provides sophisticated query analysis using OpenAI function calling
"""

import json
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import openai

from ..config.settings import get_settings
from ..config.logging_config import get_logger

class QueryIntent(Enum):
    """Specific query intents for financial data"""
    GET_STOCK_PRICE = "get_stock_price"
    GET_COMPANY_INFO = "get_company_info"
    GET_FINANCIAL_STATEMENTS = "get_financial_statements"
    COMPARE_COMPANIES = "compare_companies"
    ANALYZE_TRENDS = "analyze_trends"
    CALCULATE_METRICS = "calculate_metrics"
    GET_NEWS = "get_news"
    GENERAL_INQUIRY = "general_inquiry"

class DataRequirement(Enum):
    """Types of data required to fulfill the query"""
    REAL_TIME_PRICE = "real_time_price"
    HISTORICAL_PRICE = "historical_price"
    COMPANY_FUNDAMENTALS = "company_fundamentals"
    FINANCIAL_STATEMENTS = "financial_statements"
    MARKET_DATA = "market_data"
    NEWS_DATA = "news_data"
    CALCULATED_METRICS = "calculated_metrics"

@dataclass
class ExtractedCompany:
    """Represents a company mentioned in the query"""
    name: str
    ticker: Optional[str] = None
    confidence: float = 0.0
    aliases: List[str] = None
    exchange: Optional[str] = None
    
    def __post_init__(self):
        if self.aliases is None:
            self.aliases = []

@dataclass
class QueryParameters:
    """Parameters extracted from the query for API calls"""
    time_period: Optional[str] = None
    date_range: Optional[Tuple[str, str]] = None
    metrics: List[str] = None
    comparison_type: Optional[str] = None
    calculation_type: Optional[str] = None
    
    def __post_init__(self):
        if self.metrics is None:
            self.metrics = []

@dataclass
class QueryUnderstanding:
    """Complete understanding of a user query"""
    original_query: str
    intent: QueryIntent
    companies: List[ExtractedCompany]
    parameters: QueryParameters
    data_requirements: List[DataRequirement]
    confidence: float
    alternative_interpretations: List[Dict[str, Any]]
    suggested_api_calls: List[Dict[str, Any]]
    error_recovery_suggestions: List[str]

class QueryUnderstandingEngine:
    """Advanced query understanding using LLM with function calling"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Initialize OpenAI client
        if self.settings.has_openai_key:
            self.client = openai.AsyncOpenAI(api_key=self.settings.openai_api_key)
        else:
            self.client = None
            self.logger.warning("OpenAI API key not available - query understanding will be limited")
        
        self.logger.info("QueryUnderstandingEngine initialized")
    
    async def understand_query(self, query: str) -> QueryUnderstanding:
        """Analyze and understand a financial query comprehensively"""
        
        self.logger.info(f"Analyzing query: {query}")
        
        if not self.client:
            return self._fallback_understanding(query)
        
        try:
            # Use OpenAI function calling for structured analysis
            understanding = await self._analyze_with_openai(query)
            
            # Enhance with additional analysis
            understanding = await self._enhance_understanding(understanding)
            
            self.logger.info(f"Query understanding complete - Intent: {understanding.intent.value}, "
                           f"Companies: {len(understanding.companies)}, Confidence: {understanding.confidence:.2f}")
            
            return understanding
            
        except Exception as e:
            self.logger.error(f"Error in query understanding: {str(e)}")
            return self._fallback_understanding(query)
    
    async def _analyze_with_openai(self, query: str) -> QueryUnderstanding:
        """Use OpenAI function calling for structured query analysis"""
        
        function_schema = {
            "name": "analyze_financial_query",
            "description": "Analyze a financial query and extract structured information",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {
                        "type": "string",
                        "enum": [intent.value for intent in QueryIntent],
                        "description": "The primary intent of the query"
                    },
                    "companies": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string", "description": "Company name"},
                                "ticker": {"type": "string", "description": "Stock ticker if known"},
                                "confidence": {"type": "number", "description": "Confidence in identification (0-1)"},
                                "aliases": {"type": "array", "items": {"type": "string"}, "description": "Alternative names"},
                                "exchange": {"type": "string", "description": "Stock exchange if known"}
                            },
                            "required": ["name", "confidence"]
                        },
                        "description": "Companies mentioned in the query"
                    },
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "time_period": {"type": "string", "description": "Time period (e.g., '1d', '1m', '1y')"},
                            "date_range": {"type": "array", "items": {"type": "string"}, "description": "Start and end dates"},
                            "metrics": {"type": "array", "items": {"type": "string"}, "description": "Specific metrics requested"},
                            "comparison_type": {"type": "string", "description": "Type of comparison if applicable"},
                            "calculation_type": {"type": "string", "description": "Type of calculation if applicable"}
                        }
                    },
                    "data_requirements": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "enum": [req.value for req in DataRequirement]
                        },
                        "description": "Types of data needed to fulfill the query"
                    },
                    "confidence": {
                        "type": "number",
                        "description": "Overall confidence in the analysis (0-1)"
                    },
                    "alternative_interpretations": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "interpretation": {"type": "string"},
                                "confidence": {"type": "number"}
                            }
                        },
                        "description": "Alternative ways to interpret the query"
                    }
                },
                "required": ["intent", "companies", "data_requirements", "confidence"]
            }
        }
        
        system_prompt = """You are an expert financial query analyzer. Your job is to understand user queries about financial data and extract structured information.

Key responsibilities:
1. Identify the primary intent (what the user wants to accomplish)
2. Extract company names and try to identify their stock tickers
3. Determine what specific data is needed to answer the query
4. Extract any time periods, date ranges, or specific metrics mentioned
5. Provide confidence scores for your analysis
6. Suggest alternative interpretations if the query is ambiguous

Be thorough but practical. Focus on actionable information that can be used to make API calls."""

        response = await self.client.chat.completions.create(
            model=self.settings.openai_model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Analyze this financial query: {query}"}
            ],
            functions=[function_schema],
            function_call={"name": "analyze_financial_query"},
            max_tokens=1500,
            temperature=0.1
        )
        
        if response.choices[0].message.function_call:
            try:
                analysis = json.loads(response.choices[0].message.function_call.arguments)
                return self._build_understanding_from_analysis(query, analysis)
            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse OpenAI response: {str(e)}")
                return self._fallback_understanding(query)
        else:
            self.logger.warning("OpenAI did not return function call")
            return self._fallback_understanding(query)
    
    def _build_understanding_from_analysis(self, query: str, analysis: Dict[str, Any]) -> QueryUnderstanding:
        """Build QueryUnderstanding object from OpenAI analysis"""
        
        # Parse companies
        companies = []
        for comp_data in analysis.get("companies", []):
            company = ExtractedCompany(
                name=comp_data["name"],
                ticker=comp_data.get("ticker"),
                confidence=comp_data.get("confidence", 0.0),
                aliases=comp_data.get("aliases", []),
                exchange=comp_data.get("exchange")
            )
            companies.append(company)
        
        # Parse parameters
        params_data = analysis.get("parameters", {})
        date_range = params_data.get("date_range")
        if date_range and len(date_range) == 2:
            date_range = tuple(date_range)
        else:
            date_range = None
            
        parameters = QueryParameters(
            time_period=params_data.get("time_period"),
            date_range=date_range,
            metrics=params_data.get("metrics", []),
            comparison_type=params_data.get("comparison_type"),
            calculation_type=params_data.get("calculation_type")
        )
        
        # Parse data requirements
        data_requirements = []
        for req_str in analysis.get("data_requirements", []):
            try:
                data_requirements.append(DataRequirement(req_str))
            except ValueError:
                self.logger.warning(f"Unknown data requirement: {req_str}")
        
        # Parse intent
        try:
            intent = QueryIntent(analysis.get("intent", "general_inquiry"))
        except ValueError:
            intent = QueryIntent.GENERAL_INQUIRY
        
        return QueryUnderstanding(
            original_query=query,
            intent=intent,
            companies=companies,
            parameters=parameters,
            data_requirements=data_requirements,
            confidence=analysis.get("confidence", 0.5),
            alternative_interpretations=analysis.get("alternative_interpretations", []),
            suggested_api_calls=[],  # Will be populated by enhance_understanding
            error_recovery_suggestions=[]  # Will be populated by enhance_understanding
        )
    
    async def _enhance_understanding(self, understanding: QueryUnderstanding) -> QueryUnderstanding:
        """Enhance understanding with API call suggestions and error recovery"""
        
        # Generate suggested API calls based on intent and data requirements
        understanding.suggested_api_calls = self._generate_api_call_suggestions(understanding)
        
        # Generate error recovery suggestions
        understanding.error_recovery_suggestions = self._generate_error_recovery_suggestions(understanding)
        
        return understanding
    
    def _generate_api_call_suggestions(self, understanding: QueryUnderstanding) -> List[Dict[str, Any]]:
        """Generate specific API call suggestions based on understanding"""
        
        suggestions = []
        
        for company in understanding.companies:
            ticker = company.ticker or company.name  # Use name as fallback
            
            if understanding.intent == QueryIntent.GET_STOCK_PRICE:
                suggestions.append({
                    "api": "get_stock_price",
                    "symbol": ticker,
                    "strategy": "fastest",
                    "priority": 1
                })
            
            elif understanding.intent == QueryIntent.GET_COMPANY_INFO:
                suggestions.append({
                    "api": "get_company_fundamentals", 
                    "symbol": ticker,
                    "strategy": "best_coverage",
                    "priority": 1
                })
            
            elif understanding.intent == QueryIntent.GET_FINANCIAL_STATEMENTS:
                suggestions.append({
                    "api": "get_financial_statements",
                    "symbol": ticker,
                    "statement_type": understanding.parameters.metrics[0] if understanding.parameters.metrics else "income",
                    "strategy": "best_coverage",
                    "priority": 1
                })
        
        return suggestions
    
    def _generate_error_recovery_suggestions(self, understanding: QueryUnderstanding) -> List[str]:
        """Generate error recovery suggestions"""
        
        suggestions = []
        
        # If no companies found
        if not understanding.companies:
            suggestions.append("Try mentioning a specific company name or stock ticker symbol")
            suggestions.append("Use well-known company names like 'Apple', 'Microsoft', or 'Tesla'")
        
        # If companies found but no tickers
        companies_without_tickers = [c for c in understanding.companies if not c.ticker]
        if companies_without_tickers:
            suggestions.append("Some company names may need clarification - try using stock ticker symbols")
            suggestions.append("Check if the company is publicly traded")
        
        # If low confidence
        if understanding.confidence < 0.7:
            suggestions.append("Try rephrasing your question more specifically")
            suggestions.append("Include specific metrics or time periods you're interested in")
        
        return suggestions
    
    def _fallback_understanding(self, query: str) -> QueryUnderstanding:
        """Provide basic understanding when OpenAI is not available"""
        
        # Basic pattern matching for fallback
        query_lower = query.lower()
        
        # Determine intent
        if any(word in query_lower for word in ['price', 'cost', 'trading', 'quote']):
            intent = QueryIntent.GET_STOCK_PRICE
            data_reqs = [DataRequirement.REAL_TIME_PRICE]
        elif any(word in query_lower for word in ['fundamental', 'info', 'about', 'company']):
            intent = QueryIntent.GET_COMPANY_INFO
            data_reqs = [DataRequirement.COMPANY_FUNDAMENTALS]
        else:
            intent = QueryIntent.GENERAL_INQUIRY
            data_reqs = [DataRequirement.REAL_TIME_PRICE]
        
        return QueryUnderstanding(
            original_query=query,
            intent=intent,
            companies=[],  # Would need pattern matching to extract
            parameters=QueryParameters(),
            data_requirements=data_reqs,
            confidence=0.3,  # Low confidence for fallback
            alternative_interpretations=[],
            suggested_api_calls=[],
            error_recovery_suggestions=[
                "OpenAI integration not available - using basic query processing",
                "For better results, configure OpenAI API key"
            ]
        )
