# Financial Analyst Chatbot Environment Variables
# Copy this file to .env and fill in your API keys

# OpenAI API Key (for advanced query parsing and analysis)
OPENAI_API_KEY=your_openai_api_key_here

# Financial Data API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here

# Vector Database (Qdrant) - Optional
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your_qdrant_api_key_here
QDRANT_COLLECTION_NAME=financial_data

# Redis Cache - Optional
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password_here

# Application Configuration
APP_NAME=Financial Analyst Chatbot
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# Data Source Configuration
DATA_CACHE_TTL=300
MAX_RETRIES=3
REQUEST_TIMEOUT=30

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600
