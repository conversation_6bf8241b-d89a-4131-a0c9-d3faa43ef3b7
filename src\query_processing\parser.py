"""
Query Parser with Entity Extraction and Intent Classification
Uses regex patterns and OpenAI function calling for intelligent query analysis
"""
import re
import asyncio
import openai
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from ..config.settings import get_settings
from ..config.logging_config import get_logger

class QueryType(Enum):
    """Types of financial queries"""
    STOCK_PRICE = "stock_price"
    COMPANY_FUNDAMENTALS = "company_fundamentals"
    FINANCIAL_STATEMENTS = "financial_statements"
    COMPARISON = "comparison"
    TREND_ANALYSIS = "trend_analysis"
    CALCULATION = "calculation"
    NEWS = "news"
    GENERAL = "general"

class StatementType(Enum):
    """Types of financial statements"""
    INCOME = "income"
    BALANCE = "balance"
    CASH_FLOW = "cash_flow"

class TimeFrame(Enum):
    """Time frame for queries"""
    CURRENT = "current"
    QUARTERLY = "quarterly"
    ANNUAL = "annual"
    HISTORICAL = "historical"

@dataclass
class ExtractedEntity:
    """Extracted entity from query"""
    type: str
    value: str
    confidence: float
    start_pos: int
    end_pos: int

@dataclass
class ParsedQuery:
    """Parsed query with extracted information"""
    original_query: str
    query_type: QueryType
    entities: List[ExtractedEntity]
    symbols: List[str]
    metrics: List[str]
    time_frame: Optional[TimeFrame]
    statement_type: Optional[StatementType]
    date_range: Optional[Tuple[datetime, datetime]]
    comparison_symbols: List[str]
    confidence: float
    processing_notes: List[str]

class QueryParser:
    """Advanced query parser with entity extraction and intent classification"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("query_parser")
        
        # Initialize OpenAI client if available
        if self.settings.has_openai_key:
            self.client = openai.AsyncOpenAI(api_key=self.settings.openai_api_key)
        else:
            self.client = None
            self.logger.warning("OpenAI API key not available - using pattern-based parsing only")
        
        # Compile regex patterns for entity extraction
        self._compile_patterns()
        
        self.logger.info("QueryParser initialized")
    
    def _compile_patterns(self):
        """Compile regex patterns for entity extraction"""
        
        # Stock symbol patterns
        self.symbol_patterns = [
            r'\b([A-Z]{1,5})\b',  # Basic ticker symbols
            r'\$([A-Z]{1,5})\b',  # Symbols with $ prefix
            r'\b([A-Z]{1,5}\.NS)\b',  # NSE symbols
            r'\b([A-Z]{1,5}\.BO)\b',  # BSE symbols
        ]
        
        # Company name patterns (common companies)
        self.company_patterns = {
            'apple': 'AAPL',
            'microsoft': 'MSFT',
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'amazon': 'AMZN',
            'tesla': 'TSLA',
            'meta': 'META',
            'facebook': 'META',
            'netflix': 'NFLX',
            'nvidia': 'NVDA',
            'reliance': 'RELIANCE.NS',
            'tcs': 'TCS.NS',
            'infosys': 'INFY.NS',
            'hdfc': 'HDFCBANK.NS',
            'icici': 'ICICIBANK.NS'
        }
        
        # Financial metrics patterns
        self.metric_patterns = [
            r'\b(?:stock\s+)?price\b',
            r'\bmarket\s+cap(?:italization)?\b',
            r'\bp/e\s+ratio\b',
            r'\bearnings\b',
            r'\brevenue\b',
            r'\bdividend\b',
            r'\bvolume\b',
            r'\beps\b',
            r'\broi\b',
            r'\broa\b',
            r'\broe\b'
        ]
        
        # Time frame patterns
        self.time_patterns = {
            TimeFrame.CURRENT: [r'\bcurrent\b', r'\blatest\b', r'\btoday\b', r'\bnow\b'],
            TimeFrame.QUARTERLY: [r'\bquarterly\b', r'\bq[1-4]\b', r'\bquarter\b'],
            TimeFrame.ANNUAL: [r'\bannual\b', r'\byearly\b', r'\bfy\d{2,4}\b'],
            TimeFrame.HISTORICAL: [r'\bhistorical\b', r'\bpast\b', r'\btrend\b']
        }
        
        # Query type patterns
        self.query_type_patterns = {
            QueryType.STOCK_PRICE: [
                r'\b(?:current|latest|today\'?s?)\s+(?:stock\s+)?price\b',
                r'\bstock\s+price\b',
                r'\bshare\s+price\b',
                r'\bquote\b'
            ],
            QueryType.COMPARISON: [
                r'\bcompare\b',
                r'\bvs?\b',
                r'\bversus\b',
                r'\bdifference\s+between\b',
                r'\bwhich\s+is\s+better\b'
            ],
            QueryType.TREND_ANALYSIS: [
                r'\btrend\b',
                r'\bover\s+time\b',
                r'\bhistorical\b',
                r'\bpast\s+\d+\s+years?\b',
                r'\bgrowth\b',
                r'\bperformance\b'
            ],
            QueryType.FINANCIAL_STATEMENTS: [
                r'\bfinancial\s+statements?\b',
                r'\bincome\s+statement\b',
                r'\bbalance\s+sheet\b',
                r'\bcash\s+flow\b'
            ]
        }
    
    async def parse_query(self, query: str) -> ParsedQuery:
        """Parse a natural language query into structured information"""
        
        try:
            # Extract entities using regex patterns
            entities = await self._extract_entities(query)
            
            # Extract specific information
            symbols = self._extract_symbols(query, entities)

            # If no symbols found, try to find unknown companies using web search
            if not symbols:
                symbols = await self._find_unknown_companies(query, entities)

            metrics = self._extract_metrics(query)
            time_frame = self._extract_time_frame(query)
            statement_type = self._extract_statement_type(query)
            date_range = self._extract_date_range(query)
            
            # Classify query type using patterns and OpenAI
            query_type = await self._classify_query_type(query, entities)
            
            # Handle comparison queries
            comparison_symbols = []
            if query_type == QueryType.COMPARISON:
                comparison_symbols = symbols if len(symbols) > 1 else []
            
            # Calculate confidence score
            confidence = self._calculate_confidence(query, entities, symbols, metrics)
            
            # Generate processing notes
            processing_notes = self._generate_processing_notes(query, entities, symbols)
            
            parsed_query = ParsedQuery(
                original_query=query,
                query_type=query_type,
                entities=entities,
                symbols=symbols,
                metrics=metrics,
                time_frame=time_frame,
                statement_type=statement_type,
                date_range=date_range,
                comparison_symbols=comparison_symbols,
                confidence=confidence,
                processing_notes=processing_notes
            )
            
            self.logger.info(
                f"Query parsed successfully: type={query_type.value}, "
                f"symbols={symbols}, confidence={confidence:.2f}"
            )
            
            return parsed_query
            
        except Exception as e:
            self.logger.error(f"Error parsing query: {str(e)}")
            # Return basic parsed query on error
            return ParsedQuery(
                original_query=query,
                query_type=QueryType.GENERAL,
                entities=[],
                symbols=[],
                metrics=[],
                time_frame=None,
                statement_type=None,
                date_range=None,
                comparison_symbols=[],
                confidence=0.1,
                processing_notes=[f"Error during parsing: {str(e)}"]
            )
    
    async def _extract_entities(self, query: str) -> List[ExtractedEntity]:
        """Extract entities from query using OpenAI and patterns"""
        entities = []
        
        # Try OpenAI entity extraction first
        if self.client:
            try:
                openai_entities = await self._extract_entities_with_openai(query)
                entities.extend(openai_entities)
            except Exception as e:
                self.logger.warning(f"OpenAI entity extraction failed: {str(e)}")
        
        # Fallback to pattern-based extraction
        pattern_entities = self._extract_entities_with_patterns(query)
        entities.extend(pattern_entities)
        
        # Remove duplicates and return
        return self._deduplicate_entities(entities)
    
    def _extract_symbols(self, query: str, entities: List[ExtractedEntity]) -> List[str]:
        """Extract stock symbols from query"""
        symbols = []
        query_lower = query.lower()
        
        # Check for known company names
        for company, symbol in self.company_patterns.items():
            if company in query_lower:
                symbols.append(symbol)
        
        # Extract symbols using patterns
        for pattern in self.symbol_patterns:
            matches = re.findall(pattern, query.upper())
            symbols.extend(matches)
        
        # Extract from entities
        for entity in entities:
            if entity.type in ['STOCK_SYMBOL', 'COMPANY']:
                symbols.append(entity.value.upper())
        
        # Remove duplicates and return
        return list(set(symbols))
    
    async def _find_unknown_companies(self, query: str, entities: List[ExtractedEntity]) -> List[str]:
        """Find unknown companies using web search fallback"""
        try:
            from ..utils.web_search_fallback import web_search_fallback
            
            # Extract potential company names from entities
            company_entities = [e for e in entities if e.type in ['ORG', 'COMPANY', 'PERSON']]
            
            symbols = []
            for entity in company_entities:
                ticker = await web_search_fallback.find_missing_ticker(entity.value)
                if ticker:
                    symbols.append(ticker)
                    self.logger.info(f"Found ticker {ticker} for company {entity.value}")
            
            return symbols
            
        except Exception as e:
            self.logger.warning(f"Error finding unknown companies: {str(e)}")
            return []

    def _extract_metrics(self, query: str) -> List[str]:
        """Extract financial metrics from query"""
        metrics = []
        query_lower = query.lower()

        for pattern in self.metric_patterns:
            if re.search(pattern, query_lower):
                metrics.append(pattern.replace(r'\b', '').replace(r'\s+', ' '))

        return metrics

    def _extract_time_frame(self, query: str) -> Optional[TimeFrame]:
        """Extract time frame from query"""
        query_lower = query.lower()

        for time_frame, patterns in self.time_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return time_frame

        return TimeFrame.CURRENT  # Default to current

    def _extract_statement_type(self, query: str) -> Optional[StatementType]:
        """Extract financial statement type from query"""
        query_lower = query.lower()

        if re.search(r'\bincome\s+statement\b', query_lower):
            return StatementType.INCOME
        elif re.search(r'\bbalance\s+sheet\b', query_lower):
            return StatementType.BALANCE
        elif re.search(r'\bcash\s+flow\b', query_lower):
            return StatementType.CASH_FLOW

        return None

    def _extract_date_range(self, query: str) -> Optional[Tuple[datetime, datetime]]:
        """Extract date range from query"""
        # Simple implementation - can be enhanced
        now = datetime.now()

        if re.search(r'\blast\s+year\b', query.lower()):
            return (now - timedelta(days=365), now)
        elif re.search(r'\blast\s+quarter\b', query.lower()):
            return (now - timedelta(days=90), now)
        elif re.search(r'\blast\s+month\b', query.lower()):
            return (now - timedelta(days=30), now)

        return None

    async def _classify_query_type(self, query: str, entities: List[ExtractedEntity]) -> QueryType:
        """Classify query type using patterns and OpenAI"""
        query_lower = query.lower()

        # Check patterns first
        for query_type, patterns in self.query_type_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return query_type

        # Use OpenAI for classification if patterns don't match
        try:
            classification = await self._classify_with_openai(query)
            return classification
        except Exception as e:
            self.logger.warning(f"OpenAI classification failed: {str(e)}")
            return QueryType.GENERAL

    async def _classify_with_openai(self, query: str) -> QueryType:
        """Classify query using OpenAI function calling"""
        if not self.client:
            return QueryType.GENERAL

        function_schema = {
            "name": "classify_financial_query",
            "description": "Classify a financial query into specific types",
            "parameters": {
                "type": "object",
                "properties": {
                    "query_type": {
                        "type": "string",
                        "enum": [qt.value for qt in QueryType],
                        "description": "The type of financial query"
                    }
                },
                "required": ["query_type"]
            }
        }

        response = await self.client.chat.completions.create(
            model=self.settings.openai_model,
            messages=[
                {
                    "role": "system",
                    "content": "You are a financial query classifier. Classify queries into specific types based on their intent."
                },
                {
                    "role": "user",
                    "content": f"Classify this financial query: {query}"
                }
            ],
            functions=[function_schema],
            function_call={"name": "classify_financial_query"},
            max_tokens=200
        )

        if response.choices[0].message.function_call:
            try:
                function_args = json.loads(response.choices[0].message.function_call.arguments)
                query_type_str = function_args.get("query_type", "general")
                return QueryType(query_type_str)
            except (json.JSONDecodeError, ValueError):
                pass

        return QueryType.GENERAL

    async def _extract_entities_with_openai(self, query: str) -> List[ExtractedEntity]:
        """Extract entities using OpenAI"""
        if not self.client:
            return []

        try:
            response = await self.client.chat.completions.create(
                model=self.settings.openai_model,
                messages=[
                    {
                        "role": "system",
                        "content": "Extract financial entities from the query. Return company names, stock symbols, and financial metrics."
                    },
                    {
                        "role": "user",
                        "content": f"Extract entities from: {query}"
                    }
                ],
                max_tokens=300
            )

            # Parse the response and create entities
            # This is a simplified implementation
            entities = []
            content = response.choices[0].message.content

            # Look for patterns in the response
            if content:
                words = content.split()
                for i, word in enumerate(words):
                    if word.isupper() and len(word) <= 5:
                        entities.append(ExtractedEntity(
                            type="STOCK_SYMBOL",
                            value=word,
                            confidence=0.8,
                            start_pos=i,
                            end_pos=i+1
                        ))

            return entities

        except Exception as e:
            self.logger.error(f"OpenAI entity extraction error: {str(e)}")
            return []

    def _extract_entities_with_patterns(self, query: str) -> List[ExtractedEntity]:
        """Extract entities using regex patterns"""
        entities = []

        # Extract stock symbols
        for pattern in self.symbol_patterns:
            for match in re.finditer(pattern, query.upper()):
                entities.append(ExtractedEntity(
                    type="STOCK_SYMBOL",
                    value=match.group(1) if match.groups() else match.group(0),
                    confidence=0.9,
                    start_pos=match.start(),
                    end_pos=match.end()
                ))

        # Extract company names
        query_lower = query.lower()
        for company, symbol in self.company_patterns.items():
            if company in query_lower:
                start_pos = query_lower.find(company)
                entities.append(ExtractedEntity(
                    type="COMPANY",
                    value=company.title(),
                    confidence=0.95,
                    start_pos=start_pos,
                    end_pos=start_pos + len(company)
                ))

        return entities

    def _deduplicate_entities(self, entities: List[ExtractedEntity]) -> List[ExtractedEntity]:
        """Remove duplicate entities"""
        seen = set()
        unique_entities = []

        for entity in entities:
            key = (entity.type, entity.value.upper())
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)

        return unique_entities

    def _calculate_confidence(self, query: str, entities: List[ExtractedEntity], symbols: List[str], metrics: List[str]) -> float:
        """Calculate confidence score for the parsed query"""
        confidence = 0.0

        # Base confidence
        confidence += 0.3

        # Boost for found symbols
        if symbols:
            confidence += 0.3

        # Boost for found metrics
        if metrics:
            confidence += 0.2

        # Boost for found entities
        if entities:
            confidence += 0.2

        # Ensure confidence is between 0 and 1
        return min(1.0, confidence)

    def _generate_processing_notes(self, query: str, entities: List[ExtractedEntity], symbols: List[str]) -> List[str]:
        """Generate processing notes for debugging"""
        notes = []

        if not symbols:
            notes.append("No stock symbols found in query")

        if not entities:
            notes.append("No entities extracted from query")

        if len(query.split()) < 3:
            notes.append("Query is very short - may need more context")

        return notes
