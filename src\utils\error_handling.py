"""
Specific Error Handling System
Provides detailed error categorization and user-friendly messages
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import traceback

from ..config.logging_config import get_logger

class ErrorCategory(Enum):
    """Categories of errors that can occur"""
    INVALID_TICKER = "invalid_ticker"
    COMPANY_NOT_FOUND = "company_not_found"
    API_RATE_LIMIT = "api_rate_limit"
    API_KEY_INVALID = "api_key_invalid"
    API_KEY_MISSING = "api_key_missing"
    NETWORK_ERROR = "network_error"
    DATA_NOT_AVAILABLE = "data_not_available"
    PARSING_ERROR = "parsing_error"
    QUERY_AMBIGUOUS = "query_ambiguous"
    INSUFFICIENT_PERMISSIONS = "insufficient_permissions"
    SERVICE_UNAVAILABLE = "service_unavailable"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    """Severity levels for errors"""
    LOW = "low"           # User can easily fix
    MEDIUM = "medium"     # May require some action
    HIGH = "high"         # Significant issue
    CRITICAL = "critical" # System-level problem

@dataclass
class ErrorDetails:
    """Detailed error information"""
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    user_message: str
    suggestions: List[str]
    technical_details: Optional[str] = None
    error_code: Optional[str] = None
    retry_possible: bool = True
    estimated_fix_time: Optional[str] = None

class FinancialDataError(Exception):
    """Base exception for financial data errors"""
    
    def __init__(self, details: ErrorDetails, original_error: Exception = None):
        self.details = details
        self.original_error = original_error
        super().__init__(details.message)

class ErrorHandler:
    """Handles error categorization and user-friendly messaging"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Error patterns for automatic categorization
        self.error_patterns = {
            ErrorCategory.INVALID_TICKER: [
                "invalid symbol", "symbol not found", "ticker not found",
                "unknown symbol", "invalid ticker", "symbol does not exist"
            ],
            ErrorCategory.API_RATE_LIMIT: [
                "rate limit", "too many requests", "quota exceeded",
                "api limit", "throttled", "rate exceeded"
            ],
            ErrorCategory.API_KEY_INVALID: [
                "invalid api key", "unauthorized", "forbidden",
                "invalid key", "authentication failed", "api key invalid"
            ],
            ErrorCategory.API_KEY_MISSING: [
                "api key required", "missing api key", "no api key",
                "authentication required", "key not provided"
            ],
            ErrorCategory.NETWORK_ERROR: [
                "connection error", "network error", "timeout",
                "connection refused", "dns error", "connection timeout"
            ],
            ErrorCategory.DATA_NOT_AVAILABLE: [
                "no data available", "data not found", "no historical data",
                "insufficient data", "data unavailable", "no records found"
            ],
            ErrorCategory.SERVICE_UNAVAILABLE: [
                "service unavailable", "server error", "internal server error",
                "service down", "maintenance", "temporarily unavailable"
            ]
        }
        
        # Error message templates
        self.error_templates = {
            ErrorCategory.INVALID_TICKER: {
                "user_message": "The stock ticker '{ticker}' was not found.",
                "suggestions": [
                    "Check if the ticker symbol is spelled correctly",
                    "Try using the full company name instead",
                    "Verify that the company is publicly traded",
                    "For international stocks, include the exchange (e.g., AAPL for Apple, RELIANCE.NS for Reliance)"
                ],
                "severity": ErrorSeverity.LOW,
                "retry_possible": True,
                "estimated_fix_time": "Immediate"
            },
            ErrorCategory.COMPANY_NOT_FOUND: {
                "user_message": "Could not find ticker symbol for company '{company}'.",
                "suggestions": [
                    "Try using a more specific company name",
                    "Use the stock ticker symbol directly if known",
                    "Check if the company is publicly traded",
                    "Try alternative company names or abbreviations"
                ],
                "severity": ErrorSeverity.LOW,
                "retry_possible": True,
                "estimated_fix_time": "Immediate"
            },
            ErrorCategory.API_RATE_LIMIT: {
                "user_message": "API rate limit exceeded. Please wait before making more requests.",
                "suggestions": [
                    "Wait a few minutes before trying again",
                    "Consider upgrading to a premium API plan for higher limits",
                    "Try requesting data for fewer companies at once"
                ],
                "severity": ErrorSeverity.MEDIUM,
                "retry_possible": True,
                "estimated_fix_time": "1-5 minutes"
            },
            ErrorCategory.API_KEY_INVALID: {
                "user_message": "API authentication failed. The API key may be invalid or expired.",
                "suggestions": [
                    "Check if your API key is correctly configured",
                    "Verify that the API key hasn't expired",
                    "Ensure you have the correct permissions for this data",
                    "Contact your API provider if the issue persists"
                ],
                "severity": ErrorSeverity.HIGH,
                "retry_possible": False,
                "estimated_fix_time": "Requires configuration"
            },
            ErrorCategory.API_KEY_MISSING: {
                "user_message": "API key is required but not configured.",
                "suggestions": [
                    "Configure your API keys in the environment variables",
                    "Check the .env file for missing API keys",
                    "Sign up for free API keys from supported providers",
                    "Some features may work with free data sources"
                ],
                "severity": ErrorSeverity.HIGH,
                "retry_possible": False,
                "estimated_fix_time": "Requires configuration"
            },
            ErrorCategory.NETWORK_ERROR: {
                "user_message": "Network connection error occurred.",
                "suggestions": [
                    "Check your internet connection",
                    "Try again in a few moments",
                    "Verify that financial data services are accessible",
                    "Check if you're behind a firewall or proxy"
                ],
                "severity": ErrorSeverity.MEDIUM,
                "retry_possible": True,
                "estimated_fix_time": "1-2 minutes"
            },
            ErrorCategory.DATA_NOT_AVAILABLE: {
                "user_message": "The requested financial data is not available.",
                "suggestions": [
                    "Try a different time period or date range",
                    "Check if the company has recent trading activity",
                    "Verify that the company is still publicly traded",
                    "Some data may only be available for larger companies"
                ],
                "severity": ErrorSeverity.MEDIUM,
                "retry_possible": True,
                "estimated_fix_time": "May require different query"
            },
            ErrorCategory.PARSING_ERROR: {
                "user_message": "Could not understand your request.",
                "suggestions": [
                    "Try rephrasing your question more clearly",
                    "Include specific company names or ticker symbols",
                    "Be more specific about what information you need",
                    "Use simple, direct language"
                ],
                "severity": ErrorSeverity.LOW,
                "retry_possible": True,
                "estimated_fix_time": "Immediate"
            },
            ErrorCategory.QUERY_AMBIGUOUS: {
                "user_message": "Your request could be interpreted in multiple ways.",
                "suggestions": [
                    "Be more specific about which company you mean",
                    "Include the stock ticker symbol to avoid confusion",
                    "Specify the exact information you're looking for",
                    "Break complex requests into simpler parts"
                ],
                "severity": ErrorSeverity.LOW,
                "retry_possible": True,
                "estimated_fix_time": "Immediate"
            },
            ErrorCategory.SERVICE_UNAVAILABLE: {
                "user_message": "The financial data service is temporarily unavailable.",
                "suggestions": [
                    "Try again in a few minutes",
                    "Check if the service provider is experiencing issues",
                    "Alternative data sources may be available",
                    "Consider trying during off-peak hours"
                ],
                "severity": ErrorSeverity.HIGH,
                "retry_possible": True,
                "estimated_fix_time": "5-30 minutes"
            },
            ErrorCategory.TIMEOUT_ERROR: {
                "user_message": "Request timed out while fetching data.",
                "suggestions": [
                    "Try again with a simpler request",
                    "Check your internet connection speed",
                    "Request data for fewer companies at once",
                    "Try during off-peak hours for better performance"
                ],
                "severity": ErrorSeverity.MEDIUM,
                "retry_possible": True,
                "estimated_fix_time": "1-2 minutes"
            }
        }
    
    def categorize_error(self, error: Exception, context: Dict[str, Any] = None) -> ErrorCategory:
        """Automatically categorize an error based on its message and context"""
        
        error_message = str(error).lower()
        
        # Check patterns
        for category, patterns in self.error_patterns.items():
            for pattern in patterns:
                if pattern in error_message:
                    return category
        
        # Check specific exception types
        if isinstance(error, (ConnectionError, TimeoutError)):
            return ErrorCategory.NETWORK_ERROR
        elif isinstance(error, ValueError):
            return ErrorCategory.VALIDATION_ERROR
        elif isinstance(error, KeyError):
            return ErrorCategory.DATA_NOT_AVAILABLE
        
        return ErrorCategory.UNKNOWN_ERROR
    
    def create_error_details(
        self, 
        category: ErrorCategory, 
        context: Dict[str, Any] = None,
        original_error: Exception = None
    ) -> ErrorDetails:
        """Create detailed error information"""
        
        template = self.error_templates.get(category, {})
        context = context or {}
        
        # Format user message with context
        user_message = template.get("user_message", "An error occurred.")
        if context:
            try:
                user_message = user_message.format(**context)
            except KeyError:
                pass  # Use original message if formatting fails
        
        # Get technical details
        technical_details = None
        if original_error:
            technical_details = f"{type(original_error).__name__}: {str(original_error)}"
        
        return ErrorDetails(
            category=category,
            severity=template.get("severity", ErrorSeverity.MEDIUM),
            message=str(original_error) if original_error else user_message,
            user_message=user_message,
            suggestions=template.get("suggestions", ["Please try again later"]),
            technical_details=technical_details,
            retry_possible=template.get("retry_possible", True),
            estimated_fix_time=template.get("estimated_fix_time", "Unknown")
        )
    
    def handle_error(
        self, 
        error: Exception, 
        context: Dict[str, Any] = None,
        raise_custom: bool = True
    ) -> ErrorDetails:
        """Handle an error and return detailed information"""
        
        # Log the original error
        self.logger.error(f"Error occurred: {str(error)}", exc_info=True)
        
        # Categorize the error
        category = self.categorize_error(error, context)
        
        # Create detailed error information
        details = self.create_error_details(category, context, error)
        
        # Log the categorized error
        self.logger.info(f"Error categorized as {category.value}: {details.user_message}")
        
        if raise_custom:
            raise FinancialDataError(details, error)
        
        return details
    
    def format_error_response(self, details: ErrorDetails) -> Dict[str, Any]:
        """Format error details for API response"""
        
        return {
            "success": False,
            "error": {
                "category": details.category.value,
                "severity": details.severity.value,
                "message": details.user_message,
                "suggestions": details.suggestions,
                "retry_possible": details.retry_possible,
                "estimated_fix_time": details.estimated_fix_time,
                "error_code": details.error_code
            },
            "technical_details": details.technical_details if details.technical_details else None
        }


# Global instance
error_handler = ErrorHandler()
