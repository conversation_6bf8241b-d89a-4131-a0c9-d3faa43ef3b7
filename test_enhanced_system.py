#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced LLM query understanding system
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_enhanced_system():
    """Test all components of the enhanced system"""
    
    print("🚀 Testing Enhanced LLM Query Understanding System")
    print("=" * 60)
    
    # Test 1: Ticker Mapping Manager
    print("\n📊 Test 1: Ticker Mapping Manager")
    print("-" * 40)
    
    try:
        from src.utils.ticker_mapping_manager import ticker_mapping_manager
        
        # Test finding existing tickers
        test_companies = ["Apple", "Microsoft", "Tesla", "Amazon", "Google"]
        
        for company in test_companies:
            ticker = ticker_mapping_manager.find_ticker(company)
            if ticker:
                print(f"✅ {company} -> {ticker}")
            else:
                print(f"❌ {company} -> Not found")
        
        # Test adding a new mapping
        success = await ticker_mapping_manager.add_mapping(
            company_name="Test Company Inc",
            ticker="TEST",
            confidence=0.9,
            source="test"
        )
        
        if success:
            print("✅ Successfully added test mapping")
        else:
            print("❌ Failed to add test mapping")
        
        # Test search functionality
        search_results = ticker_mapping_manager.search_companies("apple", limit=3)
        print(f"🔍 Search results for 'apple': {len(search_results)} found")
        
        # Get stats
        stats = ticker_mapping_manager.get_mapping_stats()
        print(f"📈 Total mappings: {stats['total_mappings']}")
        
    except Exception as e:
        print(f"❌ Ticker Mapping Manager test failed: {str(e)}")
    
    # Test 2: Query Understanding Engine
    print("\n🧠 Test 2: Query Understanding Engine")
    print("-" * 40)
    
    try:
        from src.query_processing.query_understanding_engine import QueryUnderstandingEngine
        
        understanding_engine = QueryUnderstandingEngine()
        
        test_queries = [
            "What is Apple's current stock price?",
            "Show me Microsoft's financial statements",
            "Compare Tesla and Ford stock performance",
            "What are Amazon's fundamentals?",
            "Get me the latest news about Google"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: {query}")
            try:
                understanding = await understanding_engine.understand_query(query)
                
                print(f"   Intent: {understanding.intent.value}")
                print(f"   Companies: {[c.name for c in understanding.companies]}")
                print(f"   Confidence: {understanding.confidence:.2f}")
                print(f"   Data Requirements: {[req.value for req in understanding.data_requirements]}")
                
                if understanding.suggested_api_calls:
                    print(f"   Suggested APIs: {len(understanding.suggested_api_calls)}")
                
            except Exception as e:
                print(f"   ❌ Failed: {str(e)}")
        
    except Exception as e:
        print(f"❌ Query Understanding Engine test failed: {str(e)}")
    
    # Test 3: Enhanced Web Search with Ticker Discovery
    print("\n🌐 Test 3: Enhanced Web Search with Ticker Discovery")
    print("-" * 40)
    
    try:
        from src.utils.web_search_fallback import web_search_fallback
        
        # Test ticker discovery for unknown companies
        test_companies = ["Spotify", "Shopify", "Zoom"]
        
        for company in test_companies:
            print(f"\n🔍 Searching ticker for: {company}")
            ticker = await web_search_fallback.find_missing_ticker(company, store_result=True)
            
            if ticker:
                print(f"   ✅ Found: {company} -> {ticker}")
            else:
                print(f"   ❌ Not found: {company}")
        
        # Test financial data search
        print(f"\n📈 Testing financial data search")
        result = await web_search_fallback.search_financial_data(
            company_name="Apple Inc",
            ticker="AAPL",
            query_type="stock_price",
            specific_request="current stock price"
        )
        
        if result.get('success'):
            print(f"   ✅ Financial data search successful")
            print(f"   Source: {result.get('source')}")
            if result.get('data', {}).get('structured_data'):
                print(f"   Data: {result['data']['structured_data']}")
        else:
            print(f"   ❌ Financial data search failed")
        
    except Exception as e:
        print(f"❌ Web Search test failed: {str(e)}")
    
    # Test 4: Error Handling System
    print("\n⚠️  Test 4: Error Handling System")
    print("-" * 40)
    
    try:
        from src.utils.error_handling import error_handler, ErrorCategory, FinancialDataError
        
        # Test error categorization
        test_errors = [
            ("Invalid symbol INVALID123", ErrorCategory.INVALID_TICKER),
            ("Rate limit exceeded", ErrorCategory.API_RATE_LIMIT),
            ("Connection timeout", ErrorCategory.NETWORK_ERROR),
            ("API key invalid", ErrorCategory.API_KEY_INVALID)
        ]
        
        for error_msg, expected_category in test_errors:
            error = Exception(error_msg)
            category = error_handler.categorize_error(error)
            
            if category == expected_category:
                print(f"   ✅ Correctly categorized: {error_msg} -> {category.value}")
            else:
                print(f"   ❌ Incorrectly categorized: {error_msg} -> {category.value} (expected {expected_category.value})")
        
        # Test error details creation
        details = error_handler.create_error_details(
            category=ErrorCategory.INVALID_TICKER,
            context={"ticker": "INVALID123"},
            original_error=Exception("Symbol not found")
        )
        
        print(f"   📝 Error details created:")
        print(f"      Message: {details.user_message}")
        print(f"      Suggestions: {len(details.suggestions)} provided")
        print(f"      Retry possible: {details.retry_possible}")
        
    except Exception as e:
        print(f"❌ Error Handling test failed: {str(e)}")
    
    # Test 5: Enhanced Query Parser Integration
    print("\n🔧 Test 5: Enhanced Query Parser Integration")
    print("-" * 40)
    
    try:
        from src.query_processing.parser import QueryParser
        
        parser = QueryParser()
        
        test_queries = [
            "What's Apple's stock price?",
            "Show me Tesla's fundamentals",
            "I want to know about Microsoft Corporation",
            "Compare Amazon and Google",
            "What is the price of Spotify stock?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Parsing: {query}")
            try:
                parsed = await parser.parse_query(query)
                
                print(f"   Type: {parsed.query_type.value}")
                print(f"   Symbols: {parsed.symbols}")
                print(f"   Confidence: {parsed.confidence:.2f}")
                
                if hasattr(parsed, 'processing_notes') and parsed.processing_notes:
                    print(f"   Notes: {parsed.processing_notes}")
                
            except Exception as e:
                print(f"   ❌ Failed: {str(e)}")
        
    except Exception as e:
        print(f"❌ Query Parser test failed: {str(e)}")
    
    # Test 6: Data Source Manager with Enhanced Error Handling
    print("\n💾 Test 6: Data Source Manager with Enhanced Error Handling")
    print("-" * 40)
    
    try:
        from src.utils.data_source_manager import DataSourceManager, RoutingStrategy
        
        data_manager = DataSourceManager()
        
        # Test with valid ticker
        print(f"\n📊 Testing with valid ticker (AAPL)")
        response = await data_manager.get_stock_price("AAPL", RoutingStrategy.FASTEST)
        
        if response.success:
            print(f"   ✅ Success from {response.source}")
            if response.data:
                price = response.data.get('price', 'N/A')
                print(f"   Price: ${price}")
        else:
            print(f"   ❌ Failed: {response.error}")
            if hasattr(response, 'error_details') and response.error_details:
                print(f"   Category: {response.error_details.category.value}")
                print(f"   Suggestions: {response.error_details.suggestions[:2]}")
        
        # Test with invalid ticker
        print(f"\n📊 Testing with invalid ticker (INVALID123)")
        response = await data_manager.get_stock_price("INVALID123", RoutingStrategy.FASTEST)
        
        if not response.success:
            print(f"   ✅ Correctly failed: {response.error}")
            if hasattr(response, 'error_details') and response.error_details:
                print(f"   Category: {response.error_details.category.value}")
                print(f"   Retry possible: {response.error_details.retry_possible}")
        else:
            print(f"   ❌ Unexpectedly succeeded")
        
    except Exception as e:
        print(f"❌ Data Source Manager test failed: {str(e)}")
    
    print(f"\n🎉 Enhanced System Testing Complete!")
    print(f"Timestamp: {datetime.now().isoformat()}")

if __name__ == "__main__":
    asyncio.run(test_enhanced_system())
