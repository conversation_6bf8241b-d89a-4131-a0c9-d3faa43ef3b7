// Financial Chatbot JavaScript
class FinancialChatbot {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.statusDot = document.getElementById('statusDot');
        this.statusText = document.getElementById('statusText');
        
        this.isLoading = false;
        this.init();
    }

    init() {
        // Event listeners
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Check system status
        this.checkStatus();
        setInterval(() => this.checkStatus(), 30000); // Check every 30 seconds
    }

    async checkStatus() {
        try {
            const response = await fetch('/api/health');
            const data = await response.json();
            
            if (data.status === 'healthy') {
                this.statusDot.classList.add('connected');
                this.statusText.textContent = 'Connected';
            } else {
                this.statusDot.classList.remove('connected');
                this.statusText.textContent = 'Connecting...';
            }
        } catch (error) {
            this.statusDot.classList.remove('connected');
            this.statusText.textContent = 'Offline';
        }
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isLoading) return;

        // Add user message to chat
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        this.setLoading(true);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // Add bot response to chat
            this.addMessage(
                data.response, 
                'bot', 
                data.sources, 
                data.execution_time_ms
            );

        } catch (error) {
            console.error('Error:', error);
            this.addMessage(
                'I apologize, but I encountered an error while processing your request. Please try again.',
                'bot',
                ['Error Handler'],
                0
            );
        } finally {
            this.setLoading(false);
        }
    }

    addMessage(text, sender, sources = [], executionTime = 0) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const content = document.createElement('div');
        content.className = 'message-content';

        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        
        // Convert markdown-like formatting to HTML
        const formattedText = this.formatMessage(text);
        messageText.innerHTML = formattedText;

        const meta = document.createElement('div');
        meta.className = 'message-meta';
        
        const timestamp = document.createElement('span');
        timestamp.className = 'timestamp';
        timestamp.textContent = new Date().toLocaleTimeString();
        
        meta.appendChild(timestamp);

        if (sources && sources.length > 0) {
            const sourceSpan = document.createElement('span');
            sourceSpan.className = 'source';
            sourceSpan.textContent = `Sources: ${sources.join(', ')}`;
            meta.appendChild(sourceSpan);
        }

        if (executionTime > 0) {
            const timeSpan = document.createElement('span');
            timeSpan.className = 'execution-time';
            timeSpan.textContent = `${Math.round(executionTime)}ms`;
            meta.appendChild(timeSpan);
        }

        content.appendChild(messageText);
        content.appendChild(meta);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    formatMessage(text) {
        // Convert basic markdown to HTML
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>')
            .replace(/#{1,6}\s*(.*?)$/gm, '<h3>$1</h3>')
            .replace(/^\- (.*?)$/gm, '<li>$1</li>')
            .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.sendButton.disabled = loading;
        
        if (loading) {
            this.loadingOverlay.classList.add('show');
        } else {
            this.loadingOverlay.classList.remove('show');
        }
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}

// Suggestion function
function sendSuggestion(message) {
    const chatbot = window.chatbot;
    if (chatbot && !chatbot.isLoading) {
        chatbot.messageInput.value = message;
        chatbot.sendMessage();
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatbot = new FinancialChatbot();
});

// Add some visual enhancements
document.addEventListener('DOMContentLoaded', () => {
    // Add typing indicator functionality
    let typingTimer;
    const messageInput = document.getElementById('messageInput');
    
    messageInput.addEventListener('input', () => {
        clearTimeout(typingTimer);
        // Could add typing indicator here if needed
        
        typingTimer = setTimeout(() => {
            // Typing stopped
        }, 1000);
    });

    // Add smooth scrolling
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.style.scrollBehavior = 'smooth';

    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K to focus input
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            messageInput.focus();
        }
        
        // Escape to clear input
        if (e.key === 'Escape') {
            messageInput.value = '';
            messageInput.blur();
        }
    });
});
