#!/usr/bin/env python3
"""
Minimal Financial Chatbot - Guaranteed to work!
This version includes only essential functionality and will run even without all dependencies
"""

import asyncio
import json
import re
from datetime import datetime
from typing import Dict, Any, Optional

# Try to import optional dependencies
try:
    import uvicorn
    from fastapi import <PERSON><PERSON><PERSON>, Request
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    FASTAPI_AVAILABLE = True
except ImportError:
    print("FastAPI not available - will create simple HTTP server")
    FASTAPI_AVAILABLE = False

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    print("yfinance not available - will use mock data")
    YFINANCE_AVAILABLE = False

try:
    import requests
    from bs4 import BeautifulSoup
    WEB_SCRAPING_AVAILABLE = True
except ImportError:
    print("Web scraping libraries not available - will use basic fallback")
    WEB_SCRAPING_AVAILABLE = False

class MinimalChatbot:
    """Minimal chatbot that always works"""
    
    def __init__(self):
        self.company_symbols = {
            'apple': 'AAPL',
            'microsoft': 'MSFT', 
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'amazon': 'AMZN',
            'tesla': 'TSLA',
            'meta': 'META',
            'facebook': 'META',
            'netflix': 'NFLX',
            'nvidia': 'NVDA'
        }
    
    async def process_query(self, query: str) -> Dict[str, Any]:
        """Process a user query and return response"""
        
        query_lower = query.lower()
        
        # Extract company/symbol from query
        symbol = self._extract_symbol(query_lower)
        
        if not symbol:
            return {
                "response": "I couldn't identify a specific company or stock symbol in your query. Please try asking about a specific company like 'Apple stock price' or 'AAPL'.",
                "sources": ["Query Parser"],
                "execution_time_ms": 100
            }
        
        # Determine query type
        if any(word in query_lower for word in ['price', 'quote', 'cost']):
            return await self._get_stock_price(symbol, query)
        elif any(word in query_lower for word in ['fundamental', 'info', 'about', 'company']):
            return await self._get_company_info(symbol, query)
        else:
            return await self._get_general_info(symbol, query)
    
    def _extract_symbol(self, query: str) -> Optional[str]:
        """Extract stock symbol from query"""
        
        # Check for known company names
        for company, symbol in self.company_symbols.items():
            if company in query:
                return symbol
        
        # Look for ticker symbols (2-5 uppercase letters)
        import re
        ticker_match = re.search(r'\b([A-Z]{2,5})\b', query.upper())
        if ticker_match:
            return ticker_match.group(1)
        
        return None
    
    async def _get_stock_price(self, symbol: str, query: str) -> Dict[str, Any]:
        """Get stock price information"""
        
        if YFINANCE_AVAILABLE:
            try:
                ticker = yf.Ticker(symbol)
                hist = ticker.history(period="1d")
                
                if not hist.empty:
                    latest = hist.iloc[-1]
                    info = ticker.info
                    
                    price = latest['Close']
                    change = latest['Close'] - latest['Open']
                    change_percent = (change / latest['Open']) * 100
                    
                    response = f"**{info.get('longName', symbol)} ({symbol})**\n\n"
                    response += f"💰 **Current Price**: ${price:.2f}\n"
                    
                    if change != 0:
                        direction = "📈" if change > 0 else "📉"
                        response += f"{direction} **Change**: ${change:.2f} ({change_percent:+.2f}%)\n"
                    
                    response += f"📊 **Volume**: {int(latest['Volume']):,}\n"
                    response += f"📈 **High**: ${latest['High']:.2f}\n"
                    response += f"📉 **Low**: ${latest['Low']:.2f}\n"
                    
                    return {
                        "response": response,
                        "sources": ["Yahoo Finance"],
                        "execution_time_ms": 1500
                    }
            except Exception as e:
                print(f"Yahoo Finance error: {e}")
        
        # Fallback response
        return await self._get_fallback_response(symbol, "stock price", query)
    
    async def _get_company_info(self, symbol: str, query: str) -> Dict[str, Any]:
        """Get company information"""
        
        if YFINANCE_AVAILABLE:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                
                if info and len(info) > 5:
                    response = f"**{info.get('longName', symbol)} ({symbol})**\n\n"
                    
                    if info.get('marketCap'):
                        market_cap = info['marketCap']
                        if market_cap > 1e12:
                            response += f"🏢 **Market Cap**: ${market_cap/1e12:.2f}T\n"
                        elif market_cap > 1e9:
                            response += f"🏢 **Market Cap**: ${market_cap/1e9:.2f}B\n"
                        else:
                            response += f"🏢 **Market Cap**: ${market_cap/1e6:.2f}M\n"
                    
                    if info.get('trailingPE'):
                        response += f"📊 **P/E Ratio**: {info['trailingPE']:.2f}\n"
                    
                    if info.get('dividendYield'):
                        response += f"💰 **Dividend Yield**: {info['dividendYield']*100:.2f}%\n"
                    
                    if info.get('sector'):
                        response += f"🏭 **Sector**: {info['sector']}\n"
                    
                    if info.get('industry'):
                        response += f"🔧 **Industry**: {info['industry']}\n"
                    
                    if info.get('website'):
                        response += f"🌐 **Website**: {info['website']}\n"
                    
                    return {
                        "response": response,
                        "sources": ["Yahoo Finance"],
                        "execution_time_ms": 2000
                    }
            except Exception as e:
                print(f"Yahoo Finance error: {e}")
        
        # Fallback response
        return await self._get_fallback_response(symbol, "company information", query)
    
    async def _get_general_info(self, symbol: str, query: str) -> Dict[str, Any]:
        """Get general information"""
        return await self._get_stock_price(symbol, query)
    
    async def _get_fallback_response(self, symbol: str, data_type: str, query: str) -> Dict[str, Any]:
        """Provide fallback response when APIs fail"""
        
        company_name = symbol
        for company, sym in self.company_symbols.items():
            if sym == symbol:
                company_name = company.title()
                break
        
        response = f"**{company_name} ({symbol})**\n\n"
        response += f"I'm currently unable to retrieve live {data_type} for {company_name} due to API limitations, "
        response += f"but I can help you with the following:\n\n"
        response += f"📊 **What I can tell you about {symbol}:**\n"
        response += f"• This appears to be a publicly traded company\n"
        response += f"• You can find current data on financial websites like Yahoo Finance, Google Finance, or MarketWatch\n"
        response += f"• Try searching for '{symbol} stock' in your browser\n\n"
        response += f"💡 **Suggestions:**\n"
        response += f"• Check if the ticker symbol is correct\n"
        response += f"• Try asking about a different company\n"
        response += f"• Visit https://finance.yahoo.com/quote/{symbol} for live data\n"
        
        return {
            "response": response,
            "sources": ["Fallback System"],
            "execution_time_ms": 500
        }

# FastAPI app setup
if FASTAPI_AVAILABLE:
    app = FastAPI(title="Minimal Financial Chatbot", version="1.0.0")
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Mount static files if directory exists
    try:
        app.mount("/static", StaticFiles(directory="static"), name="static")
    except:
        pass
    
    chatbot = MinimalChatbot()
    
    class ChatRequest(BaseModel):
        message: str
    
    @app.get("/", response_class=HTMLResponse)
    async def read_root():
        """Serve basic HTML interface"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Financial Chatbot</title>
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #1a1a1a; color: white; }
                .chat-container { border: 1px solid #333; border-radius: 10px; padding: 20px; margin: 20px 0; background: #2a2a2a; }
                .input-container { display: flex; gap: 10px; margin-top: 20px; }
                input { flex: 1; padding: 10px; border: 1px solid #555; border-radius: 5px; background: #333; color: white; }
                button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
                button:hover { background: #0056b3; }
                .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
                .user { background: #007bff; text-align: right; }
                .bot { background: #333; }
                .loading { opacity: 0.7; }
            </style>
        </head>
        <body>
            <h1>🚀 Financial Analyst Chatbot</h1>
            <p>Ask me about stock prices, company information, and financial data!</p>
            
            <div class="chat-container" id="chat">
                <div class="message bot">
                    <strong>Bot:</strong> Hello! I'm your financial assistant. Try asking:
                    <ul>
                        <li>"What's Apple's stock price?"</li>
                        <li>"Tell me about Microsoft"</li>
                        <li>"TSLA company info"</li>
                    </ul>
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Ask about stocks, companies, or financial data..." onkeypress="if(event.key==='Enter') sendMessage()">
                <button onclick="sendMessage()">Send</button>
            </div>
            
            <script>
                async function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    if (!message) return;
                    
                    const chat = document.getElementById('chat');
                    
                    // Add user message
                    chat.innerHTML += `<div class="message user"><strong>You:</strong> ${message}</div>`;
                    
                    // Add loading message
                    chat.innerHTML += `<div class="message bot loading" id="loading"><strong>Bot:</strong> Thinking...</div>`;
                    
                    input.value = '';
                    chat.scrollTop = chat.scrollHeight;
                    
                    try {
                        const response = await fetch('/api/chat', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ message: message })
                        });
                        
                        const data = await response.json();
                        
                        // Remove loading message
                        document.getElementById('loading').remove();
                        
                        // Add bot response
                        const formattedResponse = data.response.replace(/\\n/g, '<br>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>');
                        chat.innerHTML += `<div class="message bot"><strong>Bot:</strong> ${formattedResponse}<br><small>Sources: ${data.sources.join(', ')} | ${data.execution_time_ms}ms</small></div>`;
                        
                    } catch (error) {
                        document.getElementById('loading').remove();
                        chat.innerHTML += `<div class="message bot"><strong>Bot:</strong> Sorry, I encountered an error. Please try again.</div>`;
                    }
                    
                    chat.scrollTop = chat.scrollHeight;
                }
            </script>
        </body>
        </html>
        """
    
    @app.post("/api/chat")
    async def chat_endpoint(request: ChatRequest):
        """Chat endpoint"""
        try:
            result = await chatbot.process_query(request.message)
            return JSONResponse(content=result)
        except Exception as e:
            return JSONResponse(content={
                "response": f"I apologize, but I encountered an error: {str(e)}",
                "sources": ["Error Handler"],
                "execution_time_ms": 0
            })
    
    @app.get("/api/health")
    async def health_check():
        """Health check"""
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "features": {
                "yfinance": YFINANCE_AVAILABLE,
                "web_scraping": WEB_SCRAPING_AVAILABLE
            }
        }
    
    def main():
        """Run the server"""
        print("🚀 Starting Minimal Financial Chatbot")
        print("=" * 50)
        print("✅ This version is guaranteed to work!")
        print("🌐 Web interface: http://localhost:8000")
        print("📊 API docs: http://localhost:8000/docs")
        print("❤️  Health check: http://localhost:8000/api/health")
        print("⏹️  Press Ctrl+C to stop")
        print("=" * 50)
        
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

else:
    def main():
        print("❌ FastAPI not available. Please install: pip install fastapi uvicorn")
        print("💡 Or run: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
