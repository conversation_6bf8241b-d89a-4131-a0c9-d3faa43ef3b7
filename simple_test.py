#!/usr/bin/env python3
"""
Simple test to check if basic imports work
"""

print("🧪 Testing basic imports...")

try:
    import sys
    print(f"✅ Python version: {sys.version}")
    
    import os
    print(f"✅ Current directory: {os.getcwd()}")
    
    # Test basic imports
    import fastapi
    print("✅ FastAPI imported successfully")
    
    import uvicorn
    print("✅ Uvicorn imported successfully")
    
    import aiohttp
    print("✅ aiohttp imported successfully")
    
    import yfinance
    print("✅ yfinance imported successfully")
    
    # Test our modules
    sys.path.append('src')
    
    try:
        from src.config.settings import get_settings
        settings = get_settings()
        print("✅ Settings module imported successfully")
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
    
    try:
        from src.config.logging_config import setup_logging, get_logger
        print("✅ Logging config imported successfully")
    except Exception as e:
        print(f"❌ Logging config import failed: {e}")
    
    try:
        from src.utils.web_search_fallback import web_search_fallback
        print("✅ Web search fallback imported successfully")
    except Exception as e:
        print(f"❌ Web search fallback import failed: {e}")
    
    print("\n🎉 Basic imports test completed!")
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
