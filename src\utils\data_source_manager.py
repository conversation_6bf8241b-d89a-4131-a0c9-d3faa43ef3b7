"""
Data Source Manager with Web Search Fallback
Coordinates data retrieval from multiple sources with intelligent fallback
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import aiohttp
import yfinance as yf
from enum import Enum

from ..config.settings import get_settings
from ..config.logging_config import get_logger

class DataSourceType(Enum):
    """Types of data sources"""
    ALPHA_VANTAGE = "alpha_vantage"
    FINNHUB = "finnhub"
    YAHOO_FINANCE = "yahoo_finance"
    WEB_SEARCH = "web_search"
    CACHE = "cache"

@dataclass
class DataSourceResponse:
    """Response from a data source"""
    success: bool
    data: Optional[Dict[str, Any]]
    source: str
    error: Optional[str] = None
    timestamp: Optional[datetime] = None
    cached: bool = False

class RoutingStrategy(Enum):
    """Data source routing strategies"""
    FASTEST = "fastest"
    MOST_RELIABLE = "most_reliable"
    BEST_COVERAGE = "best_coverage"
    COST_EFFECTIVE = "cost_effective"

class DataSourceManager:
    """Manages multiple data sources with intelligent routing and fallback"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Initialize web search fallback
        try:
            from .web_search_fallback import web_search_fallback
            self.web_search_fallback = web_search_fallback
            self.logger.info("Web search fallback initialized")
        except ImportError as e:
            self.logger.warning(f"Web search fallback not available: {e}")
            self.web_search_fallback = None
        
        # Data source configuration
        self.source_config = {
            DataSourceType.ALPHA_VANTAGE: {
                "enabled": self.settings.has_alpha_vantage_key,
                "priority": 1,
                "rate_limit": 5,  # requests per minute
                "reliability": 0.9
            },
            DataSourceType.FINNHUB: {
                "enabled": self.settings.has_finnhub_key,
                "priority": 2,
                "rate_limit": 60,  # requests per minute
                "reliability": 0.85
            },
            DataSourceType.YAHOO_FINANCE: {
                "enabled": True,
                "priority": 3,
                "rate_limit": 100,  # requests per minute
                "reliability": 0.8
            },
            DataSourceType.WEB_SEARCH: {
                "enabled": self.web_search_fallback is not None,
                "priority": 4,
                "rate_limit": 10,  # requests per minute
                "reliability": 0.7
            }
        }
        
        # Cache for responses
        self.cache: Dict[str, DataSourceResponse] = {}
        self.cache_ttl = timedelta(minutes=5)
        
        self.logger.info("DataSourceManager initialized")
    
    async def get_stock_price(
        self, 
        symbol: str, 
        strategy: RoutingStrategy = RoutingStrategy.FASTEST
    ) -> DataSourceResponse:
        """Get stock price with intelligent source routing"""
        
        cache_key = f"stock_price_{symbol}"
        
        # Check cache first
        cached_response = self._get_cached_response(cache_key)
        if cached_response:
            return cached_response
        
        # Get ordered list of sources based on strategy
        sources = self._get_ordered_sources(strategy)
        
        # Try each source in order
        for source_type in sources:
            if not self.source_config[source_type]["enabled"]:
                continue
                
            try:
                response = await self._get_stock_price_from_source(symbol, source_type)
                if response.success:
                    self._cache_response(cache_key, response)
                    return response
                else:
                    self.logger.warning(f"Failed response from {source_type.value}: {response.error}")
                    
            except Exception as e:
                self.logger.error(f"Error from {source_type.value}: {str(e)}")
                continue
        
        # If all sources failed, return a failure response
        return DataSourceResponse(
            success=False,
            data=None,
            source="all_sources_failed",
            error="Unable to retrieve stock price from any data source"
        )
    
    async def get_company_fundamentals(
        self,
        symbol: str,
        strategy: RoutingStrategy = RoutingStrategy.BEST_COVERAGE
    ) -> DataSourceResponse:
        """Get company fundamentals"""
        
        cache_key = f"fundamentals_{symbol}"
        
        # Check cache first
        cached_response = self._get_cached_response(cache_key)
        if cached_response:
            return cached_response
        
        # Get ordered list of sources based on strategy
        sources = self._get_ordered_sources(strategy)
        
        # Try each source in order
        for source_type in sources:
            if not self.source_config[source_type]["enabled"]:
                continue
                
            try:
                response = await self._get_fundamentals_from_source(symbol, source_type)
                if response.success:
                    self._cache_response(cache_key, response)
                    return response
                else:
                    self.logger.warning(f"Failed response from {source_type.value}: {response.error}")
                    
            except Exception as e:
                self.logger.error(f"Error from {source_type.value}: {str(e)}")
                continue
        
        # If all sources failed, return a failure response
        return DataSourceResponse(
            success=False,
            data=None,
            source="all_sources_failed",
            error="Unable to retrieve company fundamentals from any data source"
        )
    
    def _get_ordered_sources(self, strategy: RoutingStrategy) -> List[DataSourceType]:
        """Get ordered list of sources based on strategy"""
        
        if strategy == RoutingStrategy.FASTEST:
            # Order by speed (Yahoo Finance is usually fastest)
            return [
                DataSourceType.YAHOO_FINANCE,
                DataSourceType.ALPHA_VANTAGE,
                DataSourceType.FINNHUB,
                DataSourceType.WEB_SEARCH
            ]
        elif strategy == RoutingStrategy.MOST_RELIABLE:
            # Order by reliability
            return [
                DataSourceType.ALPHA_VANTAGE,
                DataSourceType.FINNHUB,
                DataSourceType.YAHOO_FINANCE,
                DataSourceType.WEB_SEARCH
            ]
        elif strategy == RoutingStrategy.BEST_COVERAGE:
            # Order by data coverage
            return [
                DataSourceType.ALPHA_VANTAGE,
                DataSourceType.FINNHUB,
                DataSourceType.YAHOO_FINANCE,
                DataSourceType.WEB_SEARCH
            ]
        else:  # COST_EFFECTIVE
            # Order by cost (free sources first)
            return [
                DataSourceType.YAHOO_FINANCE,
                DataSourceType.WEB_SEARCH,
                DataSourceType.ALPHA_VANTAGE,
                DataSourceType.FINNHUB
            ]
    
    def _get_cached_response(self, cache_key: str) -> Optional[DataSourceResponse]:
        """Get cached response if still valid"""
        if cache_key in self.cache:
            cached_response = self.cache[cache_key]
            if cached_response.timestamp and datetime.now() - cached_response.timestamp < self.cache_ttl:
                cached_response.cached = True
                return cached_response
            else:
                # Remove expired cache entry
                del self.cache[cache_key]
        return None
    
    def _cache_response(self, cache_key: str, response: DataSourceResponse):
        """Cache a response"""
        response.timestamp = datetime.now()
        self.cache[cache_key] = response
    
    async def _get_stock_price_from_source(self, symbol: str, source_type: DataSourceType) -> DataSourceResponse:
        """Get stock price from a specific source"""

        if source_type == DataSourceType.ALPHA_VANTAGE:
            return await self._get_alpha_vantage_price(symbol)
        elif source_type == DataSourceType.FINNHUB:
            return await self._get_finnhub_price(symbol)
        elif source_type == DataSourceType.YAHOO_FINANCE:
            return await self._get_yahoo_finance_price(symbol)
        elif source_type == DataSourceType.WEB_SEARCH:
            return await self._get_web_search_price(symbol)
        else:
            return DataSourceResponse(
                success=False,
                data=None,
                source=source_type.value,
                error="Unknown source type"
            )

    async def _get_fundamentals_from_source(self, symbol: str, source_type: DataSourceType) -> DataSourceResponse:
        """Get fundamentals from a specific source"""

        if source_type == DataSourceType.ALPHA_VANTAGE:
            return await self._get_alpha_vantage_fundamentals(symbol)
        elif source_type == DataSourceType.FINNHUB:
            return await self._get_finnhub_fundamentals(symbol)
        elif source_type == DataSourceType.YAHOO_FINANCE:
            return await self._get_yahoo_finance_fundamentals(symbol)
        elif source_type == DataSourceType.WEB_SEARCH:
            return await self._get_web_search_fundamentals(symbol)
        else:
            return DataSourceResponse(
                success=False,
                data=None,
                source=source_type.value,
                error="Unknown source type"
            )

    async def _get_alpha_vantage_price(self, symbol: str) -> DataSourceResponse:
        """Get stock price from Alpha Vantage"""
        try:
            if not self.settings.has_alpha_vantage_key:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="alpha_vantage",
                    error="Alpha Vantage API key not configured"
                )

            url = f"https://www.alphavantage.co/query"
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": symbol,
                "apikey": self.settings.alpha_vantage_api_key
            }

            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if "Global Quote" in data:
                        quote = data["Global Quote"]
                        return DataSourceResponse(
                            success=True,
                            data={
                                "symbol": quote.get("01. symbol", symbol),
                                "price": float(quote.get("05. price", 0)),
                                "change": float(quote.get("09. change", 0)),
                                "change_percent": quote.get("10. change percent", "0%"),
                                "volume": int(quote.get("06. volume", 0)),
                                "latest_trading_day": quote.get("07. latest trading day", "")
                            },
                            source="alpha_vantage"
                        )
                    else:
                        return DataSourceResponse(
                            success=False,
                            data=None,
                            source="alpha_vantage",
                            error="Invalid response format from Alpha Vantage"
                        )
                else:
                    return DataSourceResponse(
                        success=False,
                        data=None,
                        source="alpha_vantage",
                        error=f"HTTP {response.status}"
                    )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="alpha_vantage",
                error=f"API call failed: {str(e)}"
            )

    async def _get_finnhub_price(self, symbol: str) -> DataSourceResponse:
        """Get stock price from Finnhub"""
        try:
            if not self.settings.has_finnhub_key:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="finnhub",
                    error="Finnhub API key not configured"
                )

            url = f"https://finnhub.io/api/v1/quote"
            params = {
                "symbol": symbol,
                "token": self.settings.finnhub_api_key
            }

            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if "c" in data:  # current price
                        return DataSourceResponse(
                            success=True,
                            data={
                                "symbol": symbol,
                                "price": float(data.get("c", 0)),
                                "change": float(data.get("d", 0)),
                                "change_percent": f"{float(data.get('dp', 0)):.2f}%",
                                "high": float(data.get("h", 0)),
                                "low": float(data.get("l", 0)),
                                "open": float(data.get("o", 0)),
                                "previous_close": float(data.get("pc", 0))
                            },
                            source="finnhub"
                        )
                    else:
                        return DataSourceResponse(
                            success=False,
                            data=None,
                            source="finnhub",
                            error="No data found for symbol"
                        )
                else:
                    error_data = await response.json()
                    return DataSourceResponse(
                        success=False,
                        data=None,
                        source="finnhub",
                        error=f"HTTP {response.status}: {error_data.get('error', 'Unknown error')}"
                    )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="finnhub",
                error=f"API call failed: {str(e)}"
            )

    async def _get_yahoo_finance_price(self, symbol: str) -> DataSourceResponse:
        """Get stock price from Yahoo Finance"""
        try:
            # Use yfinance library
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1d")

            if hist.empty:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="yahoo_finance",
                    error=f"Failed to fetch historical data for {symbol}: No data available"
                )

            latest = hist.iloc[-1]
            info = ticker.info

            return DataSourceResponse(
                success=True,
                data={
                    "symbol": symbol,
                    "price": float(latest['Close']),
                    "change": float(latest['Close'] - latest['Open']),
                    "volume": int(latest['Volume']),
                    "high": float(latest['High']),
                    "low": float(latest['Low']),
                    "open": float(latest['Open']),
                    "company_name": info.get('longName', symbol)
                },
                source="yahoo_finance"
            )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="yahoo_finance",
                error=f"API call failed: {str(e)}"
            )

    async def _get_web_search_price(self, symbol: str) -> DataSourceResponse:
        """Get stock price using web search fallback"""
        try:
            if not self.web_search_fallback:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="web_search",
                    error="Web search fallback not available"
                )

            # Try to get company name for better search
            company_name = symbol  # Default to symbol

            result = await self.web_search_fallback.search_financial_data(
                company_name=company_name,
                ticker=symbol,
                query_type="stock_price",
                specific_request=f"current stock price for {symbol}"
            )

            if result and result.get('success'):
                return DataSourceResponse(
                    success=True,
                    data=result.get('data', {}),
                    source="Web Search"
                )
            else:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="web_search",
                    error=result.get('error', 'Web search failed') if result else 'No result from web search'
                )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="web_search",
                error=f"Web search failed: {str(e)}"
            )

    async def _get_alpha_vantage_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get fundamentals from Alpha Vantage"""
        try:
            if not self.settings.has_alpha_vantage_key:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="alpha_vantage",
                    error="Alpha Vantage API key not configured"
                )

            url = f"https://www.alphavantage.co/query"
            params = {
                "function": "OVERVIEW",
                "symbol": symbol,
                "apikey": self.settings.alpha_vantage_api_key
            }

            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if "Symbol" in data:
                        return DataSourceResponse(
                            success=True,
                            data={
                                "symbol": data.get("Symbol", symbol),
                                "company_name": data.get("Name", ""),
                                "market_cap": data.get("MarketCapitalization", ""),
                                "pe_ratio": data.get("PERatio", ""),
                                "dividend_yield": data.get("DividendYield", ""),
                                "eps": data.get("EPS", ""),
                                "revenue": data.get("RevenueTTM", ""),
                                "sector": data.get("Sector", ""),
                                "industry": data.get("Industry", "")
                            },
                            source="alpha_vantage"
                        )
                    else:
                        return DataSourceResponse(
                            success=False,
                            data=None,
                            source="alpha_vantage",
                            error="No fundamental data found for symbol"
                        )
                else:
                    return DataSourceResponse(
                        success=False,
                        data=None,
                        source="alpha_vantage",
                        error=f"HTTP {response.status}"
                    )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="alpha_vantage",
                error=f"API call failed: {str(e)}"
            )

    async def _get_finnhub_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get fundamentals from Finnhub"""
        try:
            if not self.settings.has_finnhub_key:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="finnhub",
                    error="Finnhub API key not configured"
                )

            # Get company profile
            url = f"https://finnhub.io/api/v1/stock/profile2"
            params = {
                "symbol": symbol,
                "token": self.settings.finnhub_api_key
            }

            if not self.session:
                self.session = aiohttp.ClientSession()

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if data and "name" in data:
                        return DataSourceResponse(
                            success=True,
                            data={
                                "symbol": symbol,
                                "company_name": data.get("name", ""),
                                "market_cap": data.get("marketCapitalization", ""),
                                "country": data.get("country", ""),
                                "currency": data.get("currency", ""),
                                "exchange": data.get("exchange", ""),
                                "industry": data.get("finnhubIndustry", ""),
                                "website": data.get("weburl", ""),
                                "employees": data.get("shareOutstanding", "")
                            },
                            source="finnhub"
                        )
                    else:
                        return DataSourceResponse(
                            success=False,
                            data=None,
                            source="finnhub",
                            error="No fundamental data found for symbol"
                        )
                else:
                    error_data = await response.json()
                    return DataSourceResponse(
                        success=False,
                        data=None,
                        source="finnhub",
                        error=f"HTTP {response.status}: {error_data.get('error', 'Unknown error')}"
                    )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="finnhub",
                error=f"API call failed: {str(e)}"
            )

    async def _get_yahoo_finance_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get fundamentals from Yahoo Finance"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info

            if not info or len(info) < 5:  # Basic check for valid data
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="yahoo_finance",
                    error=f"Failed to fetch data for {symbol}: No data available"
                )

            return DataSourceResponse(
                success=True,
                data={
                    "symbol": symbol,
                    "company_name": info.get("longName", ""),
                    "market_cap": info.get("marketCap", ""),
                    "pe_ratio": info.get("trailingPE", ""),
                    "dividend_yield": info.get("dividendYield", ""),
                    "eps": info.get("trailingEps", ""),
                    "revenue": info.get("totalRevenue", ""),
                    "sector": info.get("sector", ""),
                    "industry": info.get("industry", ""),
                    "employees": info.get("fullTimeEmployees", ""),
                    "website": info.get("website", "")
                },
                source="yahoo_finance"
            )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="yahoo_finance",
                error=f"API call failed: {str(e)}"
            )

    async def _get_web_search_fundamentals(self, symbol: str) -> DataSourceResponse:
        """Get fundamentals using web search fallback"""
        try:
            if not self.web_search_fallback:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="web_search",
                    error="Web search fallback not available"
                )

            # Try to get company name for better search
            company_name = symbol  # Default to symbol

            result = await self.web_search_fallback.search_financial_data(
                company_name=company_name,
                ticker=symbol,
                query_type="company_fundamentals",
                specific_request=f"company fundamentals for {symbol}"
            )

            if result and result.get('success'):
                return DataSourceResponse(
                    success=True,
                    data=result.get('data', {}),
                    source="Web Search"
                )
            else:
                return DataSourceResponse(
                    success=False,
                    data=None,
                    source="web_search",
                    error=result.get('error', 'Web search failed') if result else 'No result from web search'
                )

        except Exception as e:
            return DataSourceResponse(
                success=False,
                data=None,
                source="web_search",
                error=f"Web search failed: {str(e)}"
            )

    async def get_system_status(self) -> Dict[str, Any]:
        """Get system status"""
        return {
            "status": "operational",
            "timestamp": datetime.now().isoformat(),
            "sources": {
                source_type.value: config["enabled"]
                for source_type, config in self.source_config.items()
            },
            "cache_size": len(self.cache),
            "web_search_available": self.web_search_fallback is not None
        }
