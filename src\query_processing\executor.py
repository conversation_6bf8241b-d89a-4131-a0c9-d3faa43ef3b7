"""
Query Executor
Orchestrates parallel API calls and coordinates data retrieval from multiple sources
"""
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .parser import Parsed<PERSON>uery, QueryType
else:
    # Import at runtime to avoid circular imports
    ParsedQuery = None
    QueryType = None
from ..utils.data_source_manager import DataSourceManager, RoutingStrategy
from ..config.logging_config import get_logger

@dataclass
class ExecutionResult:
    """Result of query execution"""
    success: bool
    data: Dict[str, Any]
    sources_used: List[str]
    execution_time_ms: float
    errors: List[str]
    warnings: List[str]
    metadata: Dict[str, Any]

@dataclass
class ExecutionPlan:
    """Plan for executing a query"""
    query_type: str  # Changed from QueryType to str to avoid circular import
    primary_operations: List[Tuple[str, Any]]  # (description, operation)
    parallel_operations: List[Tuple[str, Any]]
    fallback_operations: List[Tuple[str, Any]]
    estimated_time_ms: float
    required_data: List[str]

class QueryExecutor:
    """Orchestrates query execution with parallel API calls and error handling"""
    
    def __init__(self, data_source_manager: DataSourceManager):
        self.data_manager = data_source_manager
        self.logger = get_logger("query_executor")
        
        # Execution statistics
        self.execution_stats = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "avg_execution_time_ms": 0.0,
            "queries_by_type": {}
        }
    
    async def execute_query(self, parsed_query) -> ExecutionResult:
        """Execute a parsed query with optimal orchestration"""
        start_time = time.time()
        
        self.logger.info(
            f"Executing query: type={parsed_query.query_type.value}, "
            f"symbols={parsed_query.symbols}, confidence={parsed_query.confidence:.2f}"
        )
        
        try:
            # Create execution plan
            execution_plan = self._create_execution_plan(parsed_query)
            
            # Execute the plan
            result = await self._execute_plan(parsed_query, execution_plan)
            
            # Update statistics
            execution_time = (time.time() - start_time) * 1000
            self._update_stats(parsed_query.query_type, True, execution_time)
            
            result.execution_time_ms = execution_time
            
            self.logger.info(
                f"Query executed successfully in {execution_time:.0f}ms, "
                f"sources used: {result.sources_used}"
            )
            
            return result
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self._update_stats(parsed_query.query_type, False, execution_time)
            
            self.logger.error(f"Query execution failed: {str(e)}")
            
            return ExecutionResult(
                success=False,
                data={},
                sources_used=[],
                execution_time_ms=execution_time,
                errors=[str(e)],
                warnings=[],
                metadata={"query_type": getattr(parsed_query.query_type, 'value', str(parsed_query.query_type))}
            )
    
    def _create_execution_plan(self, parsed_query) -> ExecutionPlan:
        """Create an execution plan based on the parsed query"""
        
        query_type_value = getattr(parsed_query.query_type, 'value', str(parsed_query.query_type))

        if query_type_value == "stock_price":
            return self._plan_stock_price_query(parsed_query)
        elif query_type_value == "company_fundamentals":
            return self._plan_fundamentals_query(parsed_query)
        elif query_type_value == "financial_statements":
            return self._plan_statements_query(parsed_query)
        elif query_type_value == "comparison":
            return self._plan_comparison_query(parsed_query)
        elif query_type_value == "trend_analysis":
            return self._plan_trend_analysis_query(parsed_query)
        else:
            return self._plan_general_query(parsed_query)
    
    def _plan_stock_price_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for stock price queries"""
        operations = []
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get stock price for {symbol}",
                lambda s=symbol: self.data_manager.get_stock_price(s, RoutingStrategy.FASTEST)
            ))
        
        return ExecutionPlan(
            query_type="stock_price",
            primary_operations=operations,
            parallel_operations=operations if len(operations) > 1 else [],
            fallback_operations=[],
            estimated_time_ms=2000,
            required_data=["price", "change", "volume"]
        )
    
    def _plan_fundamentals_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for fundamentals queries"""
        operations = []
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get fundamentals for {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type="company_fundamentals",
            primary_operations=operations,
            parallel_operations=operations if len(operations) > 1 else [],
            fallback_operations=[],
            estimated_time_ms=3000,
            required_data=["market_cap", "pe_ratio", "revenue"]
        )
    
    def _plan_statements_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for financial statements queries"""
        operations = []
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get financial statements for {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type="financial_statements",
            primary_operations=operations,
            parallel_operations=[],
            fallback_operations=[],
            estimated_time_ms=4000,
            required_data=["revenue", "profit", "assets"]
        )
    
    def _plan_comparison_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for comparison queries"""
        operations = []
        
        # Get data for all symbols to compare
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get data for comparison: {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type="comparison",
            primary_operations=operations,
            parallel_operations=operations,  # Run in parallel for comparison
            fallback_operations=[],
            estimated_time_ms=3000,
            required_data=["price", "market_cap", "pe_ratio"]
        )
    
    def _plan_trend_analysis_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for trend analysis queries"""
        operations = []
        
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get historical data for {symbol}",
                lambda s=symbol: self.data_manager.get_stock_price(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type="trend_analysis",
            primary_operations=operations,
            parallel_operations=[],
            fallback_operations=[],
            estimated_time_ms=5000,
            required_data=["historical_prices", "volume", "trends"]
        )
    
    def _plan_general_query(self, parsed_query: ParsedQuery) -> ExecutionPlan:
        """Plan execution for general queries"""
        operations = []
        
        # Try to get basic information for any symbols found
        for symbol in parsed_query.symbols:
            operations.append((
                f"Get basic info for {symbol}",
                lambda s=symbol: self.data_manager.get_company_fundamentals(s, RoutingStrategy.BEST_COVERAGE)
            ))
        
        return ExecutionPlan(
            query_type="general",
            primary_operations=operations,
            parallel_operations=[],
            fallback_operations=[],
            estimated_time_ms=3000,
            required_data=["basic_info"]
        )
    
    async def _execute_plan(self, parsed_query: ParsedQuery, plan: ExecutionPlan) -> ExecutionResult:
        """Execute the execution plan"""
        results = {}
        sources_used = []
        errors = []
        warnings = []
        
        # Execute primary operations sequentially
        for description, operation in plan.primary_operations:
            try:
                self.logger.debug(f"Executing: {description}")
                response = await operation()
                
                if response.success:
                    results[description] = response.data
                    sources_used.append(response.source)
                else:
                    errors.append(f"{description}: {response.error}")
                    
            except Exception as e:
                errors.append(f"{description}: {str(e)}")
        
        # Execute parallel operations if any
        if plan.parallel_operations:
            try:
                parallel_tasks = []
                for description, operation in plan.parallel_operations:
                    parallel_tasks.append(self._execute_with_description(description, operation))
                
                parallel_results = await asyncio.gather(*parallel_tasks, return_exceptions=True)
                
                for i, result in enumerate(parallel_results):
                    description = plan.parallel_operations[i][0]
                    if isinstance(result, Exception):
                        errors.append(f"{description}: {str(result)}")
                    elif result and result.success:
                        results[description] = result.data
                        sources_used.append(result.source)
                    else:
                        errors.append(f"{description}: Failed to get data")
                        
            except Exception as e:
                errors.append(f"Parallel execution failed: {str(e)}")
        
        # Determine overall success
        success = len(results) > 0
        
        # Prepare metadata
        metadata = {
            "query_type": plan.query_type.value,
            "symbols_requested": parsed_query.symbols,
            "operations_planned": len(plan.primary_operations) + len(plan.parallel_operations),
            "operations_successful": len(results),
            "estimated_time_ms": plan.estimated_time_ms,
            "required_data": plan.required_data
        }
        
        return ExecutionResult(
            success=success,
            data=results,
            sources_used=list(set(sources_used)),  # Remove duplicates
            execution_time_ms=0,  # Will be set by caller
            errors=errors,
            warnings=warnings,
            metadata=metadata
        )
    
    async def _execute_with_description(self, description: str, operation) -> Any:
        """Execute an operation with description for parallel execution"""
        try:
            return await operation()
        except Exception as e:
            self.logger.error(f"Operation '{description}' failed: {str(e)}")
            raise
    
    def _update_stats(self, query_type, success: bool, execution_time_ms: float):
        """Update execution statistics"""
        self.execution_stats["total_queries"] += 1
        
        if success:
            self.execution_stats["successful_queries"] += 1
        else:
            self.execution_stats["failed_queries"] += 1
        
        # Update average execution time
        total_time = (self.execution_stats["avg_execution_time_ms"] * 
                     (self.execution_stats["total_queries"] - 1) + execution_time_ms)
        self.execution_stats["avg_execution_time_ms"] = total_time / self.execution_stats["total_queries"]
        
        # Update query type stats
        query_type_value = getattr(query_type, 'value', str(query_type))
        if query_type_value not in self.execution_stats["queries_by_type"]:
            self.execution_stats["queries_by_type"][query_type_value] = 0
        self.execution_stats["queries_by_type"][query_type_value] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get execution statistics"""
        return self.execution_stats.copy()
