#!/usr/bin/env python3
"""
Quick Start Script for Financial Analyst Chatbot
Provides an easy way to start the chatbot with minimal configuration
"""

import os
import sys
from pathlib import Path

def main():
    """Main startup function"""
    print("🚀 Starting Financial Analyst Chatbot")
    print("=" * 50)
    
    # Check if .env file exists, if not create a basic one
    env_file = Path(".env")
    if not env_file.exists():
        print("📝 Creating basic .env file...")
        with open(".env", "w") as f:
            f.write("""# Basic configuration for Financial Analyst Chatbot
# You can add your API keys here for enhanced functionality

# OpenAI API Key (optional - for advanced features)
OPENAI_API_KEY=

# Financial Data API Keys (optional - for primary data sources)
ALPHA_VANTAGE_API_KEY=
FINNHUB_API_KEY=

# Application Configuration
DEBUG=false
LOG_LEVEL=INFO
""")
        print("✅ Created .env file. You can add your API keys there for enhanced functionality.")
    
    # Check if required directories exist
    required_dirs = ["logs", "static", "src"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            print(f"📁 Created directory: {dir_name}")
    
    print("\n🌟 The chatbot will work with web search fallback even without API keys!")
    print("💡 For enhanced functionality, add your API keys to the .env file")
    print("\n🔗 Starting server...")
    print("   - Web interface: http://localhost:8000")
    print("   - API docs: http://localhost:8000/docs")
    print("   - Health check: http://localhost:8000/api/health")
    print("\n⏹️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Import and run the main application
    try:
        from run_chatbot import main as run_main
        run_main()
    except KeyboardInterrupt:
        print("\n👋 Shutting down Financial Analyst Chatbot")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("💡 Try running: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
