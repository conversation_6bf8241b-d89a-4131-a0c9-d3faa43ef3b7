"""
Ticker Mapping Manager
Persistent storage and retrieval of company name to ticker symbol mappings
"""

import json
import asyncio
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import difflib
import re

from ..config.settings import get_settings
from ..config.logging_config import get_logger

@dataclass
class TickerMapping:
    """Represents a company name to ticker mapping"""
    company_name: str
    ticker: str
    confidence: float
    source: str
    discovery_date: str
    last_verified: Optional[str] = None
    aliases: List[str] = None
    exchange: Optional[str] = None
    market_cap: Optional[float] = None
    sector: Optional[str] = None
    
    def __post_init__(self):
        if self.aliases is None:
            self.aliases = []

class TickerMappingManager:
    """Manages persistent storage of company-ticker mappings"""
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger(__name__)
        
        # Storage file path
        self.storage_file = Path(self.settings.data_dir) / "ticker_mappings.json"
        
        # In-memory cache
        self.mappings: Dict[str, TickerMapping] = {}
        self.ticker_to_company: Dict[str, str] = {}
        
        # Built-in mappings for common companies
        self.builtin_mappings = {
            'apple': 'AAPL',
            'apple inc': 'AAPL',
            'microsoft': 'MSFT',
            'microsoft corporation': 'MSFT',
            'google': 'GOOGL',
            'alphabet': 'GOOGL',
            'alphabet inc': 'GOOGL',
            'amazon': 'AMZN',
            'amazon.com': 'AMZN',
            'tesla': 'TSLA',
            'tesla inc': 'TSLA',
            'meta': 'META',
            'meta platforms': 'META',
            'facebook': 'META',
            'netflix': 'NFLX',
            'nvidia': 'NVDA',
            'nvidia corporation': 'NVDA',
            'berkshire hathaway': 'BRK-A',
            'johnson & johnson': 'JNJ',
            'jpmorgan chase': 'JPM',
            'visa': 'V',
            'procter & gamble': 'PG',
            'unitedhealth': 'UNH',
            'home depot': 'HD',
            'mastercard': 'MA',
            'bank of america': 'BAC',
            'disney': 'DIS',
            'walt disney': 'DIS',
            'coca cola': 'KO',
            'coca-cola': 'KO',
            'pepsi': 'PEP',
            'pepsico': 'PEP',
            'walmart': 'WMT',
            'intel': 'INTC',
            'cisco': 'CSCO',
            'oracle': 'ORCL',
            'salesforce': 'CRM',
            'adobe': 'ADBE',
            'ibm': 'IBM',
            'international business machines': 'IBM',
            # Indian companies
            'reliance': 'RELIANCE.NS',
            'reliance industries': 'RELIANCE.NS',
            'tcs': 'TCS.NS',
            'tata consultancy services': 'TCS.NS',
            'infosys': 'INFY.NS',
            'hdfc bank': 'HDFCBANK.NS',
            'icici bank': 'ICICIBANK.NS',
            'state bank of india': 'SBIN.NS',
            'sbi': 'SBIN.NS',
            'bharti airtel': 'BHARTIARTL.NS',
            'airtel': 'BHARTIARTL.NS',
            'itc': 'ITC.NS',
            'hindustan unilever': 'HINDUNILVR.NS',
            'hul': 'HINDUNILVR.NS',
            'bajaj finance': 'BAJFINANCE.NS',
            'asian paints': 'ASIANPAINT.NS',
            'maruti suzuki': 'MARUTI.NS',
            'maruti': 'MARUTI.NS'
        }
        
        # Load existing mappings
        self._load_mappings()
        
        self.logger.info(f"TickerMappingManager initialized with {len(self.mappings)} stored mappings")
    
    def _load_mappings(self):
        """Load mappings from storage file"""
        try:
            if self.storage_file.exists():
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for mapping_data in data.get('mappings', []):
                    mapping = TickerMapping(**mapping_data)
                    self.mappings[mapping.company_name.lower()] = mapping
                    self.ticker_to_company[mapping.ticker.upper()] = mapping.company_name
                
                self.logger.info(f"Loaded {len(self.mappings)} mappings from storage")
            
            # Add built-in mappings if not already present
            self._add_builtin_mappings()
            
        except Exception as e:
            self.logger.error(f"Error loading mappings: {str(e)}")
            self._add_builtin_mappings()
    
    def _add_builtin_mappings(self):
        """Add built-in mappings for common companies"""
        added_count = 0
        
        for company_name, ticker in self.builtin_mappings.items():
            if company_name.lower() not in self.mappings:
                mapping = TickerMapping(
                    company_name=company_name,
                    ticker=ticker,
                    confidence=1.0,
                    source="builtin",
                    discovery_date=datetime.now().isoformat(),
                    last_verified=datetime.now().isoformat()
                )
                self.mappings[company_name.lower()] = mapping
                self.ticker_to_company[ticker.upper()] = company_name
                added_count += 1
        
        if added_count > 0:
            self.logger.info(f"Added {added_count} built-in mappings")
            self._save_mappings()
    
    def _save_mappings(self):
        """Save mappings to storage file"""
        try:
            # Ensure directory exists
            self.storage_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert mappings to serializable format
            data = {
                'version': '1.0',
                'last_updated': datetime.now().isoformat(),
                'mappings': [asdict(mapping) for mapping in self.mappings.values()]
            }
            
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Saved {len(self.mappings)} mappings to storage")
            
        except Exception as e:
            self.logger.error(f"Error saving mappings: {str(e)}")
    
    def find_ticker(self, company_name: str) -> Optional[str]:
        """Find ticker for a company name with fuzzy matching"""
        
        if not company_name:
            return None
        
        company_clean = self._clean_company_name(company_name)
        
        # Exact match
        if company_clean in self.mappings:
            mapping = self.mappings[company_clean]
            self.logger.debug(f"Exact match: {company_name} -> {mapping.ticker}")
            return mapping.ticker
        
        # Fuzzy matching
        best_match = self._fuzzy_match(company_clean)
        if best_match:
            self.logger.debug(f"Fuzzy match: {company_name} -> {best_match}")
            return best_match
        
        self.logger.debug(f"No ticker found for: {company_name}")
        return None
    
    def _clean_company_name(self, name: str) -> str:
        """Clean and normalize company name for matching"""
        
        # Convert to lowercase
        clean = name.lower().strip()
        
        # Remove common suffixes
        suffixes = [
            'inc', 'inc.', 'corp', 'corp.', 'corporation', 'company', 'co', 'co.',
            'ltd', 'ltd.', 'limited', 'llc', 'l.l.c.', 'plc', 'p.l.c.',
            'group', 'holdings', 'international', 'intl', 'technologies', 'tech',
            'systems', 'solutions', 'services', 'enterprises'
        ]
        
        for suffix in suffixes:
            if clean.endswith(f' {suffix}'):
                clean = clean[:-len(suffix)-1].strip()
        
        # Remove special characters but keep spaces and hyphens
        clean = re.sub(r'[^\w\s\-&]', '', clean)
        
        # Normalize whitespace
        clean = ' '.join(clean.split())
        
        return clean
    
    def _fuzzy_match(self, company_name: str) -> Optional[str]:
        """Find best fuzzy match for company name"""
        
        best_ratio = 0.0
        best_ticker = None
        
        # Check against stored mappings
        for stored_name, mapping in self.mappings.items():
            ratio = difflib.SequenceMatcher(None, company_name, stored_name).ratio()
            
            if ratio > best_ratio and ratio >= 0.8:  # 80% similarity threshold
                best_ratio = ratio
                best_ticker = mapping.ticker
            
            # Also check aliases
            for alias in mapping.aliases:
                alias_clean = self._clean_company_name(alias)
                ratio = difflib.SequenceMatcher(None, company_name, alias_clean).ratio()
                
                if ratio > best_ratio and ratio >= 0.8:
                    best_ratio = ratio
                    best_ticker = mapping.ticker
        
        return best_ticker if best_ratio >= 0.8 else None
    
    async def add_mapping(
        self, 
        company_name: str, 
        ticker: str, 
        confidence: float = 1.0,
        source: str = "discovered",
        aliases: List[str] = None,
        exchange: str = None,
        **metadata
    ) -> bool:
        """Add a new company-ticker mapping"""
        
        try:
            company_clean = self._clean_company_name(company_name)
            ticker_upper = ticker.upper()
            
            # Check if mapping already exists
            if company_clean in self.mappings:
                existing = self.mappings[company_clean]
                if existing.ticker == ticker_upper:
                    # Update last verified
                    existing.last_verified = datetime.now().isoformat()
                    self._save_mappings()
                    self.logger.debug(f"Updated verification for {company_name} -> {ticker}")
                    return True
                else:
                    self.logger.warning(f"Conflicting mapping for {company_name}: existing={existing.ticker}, new={ticker}")
                    return False
            
            # Create new mapping
            mapping = TickerMapping(
                company_name=company_name,
                ticker=ticker_upper,
                confidence=confidence,
                source=source,
                discovery_date=datetime.now().isoformat(),
                last_verified=datetime.now().isoformat(),
                aliases=aliases or [],
                exchange=exchange,
                **metadata
            )
            
            # Store mapping
            self.mappings[company_clean] = mapping
            self.ticker_to_company[ticker_upper] = company_name
            
            # Save to file
            self._save_mappings()
            
            self.logger.info(f"Added new mapping: {company_name} -> {ticker} (confidence: {confidence:.2f})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding mapping {company_name} -> {ticker}: {str(e)}")
            return False
    
    def get_company_name(self, ticker: str) -> Optional[str]:
        """Get company name for a ticker symbol"""
        
        ticker_upper = ticker.upper()
        return self.ticker_to_company.get(ticker_upper)
    
    def search_companies(self, query: str, limit: int = 10) -> List[Tuple[str, str, float]]:
        """Search for companies matching a query"""
        
        query_clean = self._clean_company_name(query)
        results = []
        
        for stored_name, mapping in self.mappings.items():
            # Calculate similarity
            ratio = difflib.SequenceMatcher(None, query_clean, stored_name).ratio()
            
            if ratio >= 0.3:  # Lower threshold for search
                results.append((mapping.company_name, mapping.ticker, ratio))
            
            # Also check aliases
            for alias in mapping.aliases:
                alias_clean = self._clean_company_name(alias)
                ratio = difflib.SequenceMatcher(None, query_clean, alias_clean).ratio()
                
                if ratio >= 0.3:
                    results.append((mapping.company_name, mapping.ticker, ratio))
        
        # Sort by similarity and remove duplicates
        results = list(set(results))
        results.sort(key=lambda x: x[2], reverse=True)
        
        return results[:limit]
    
    def get_mapping_stats(self) -> Dict[str, Any]:
        """Get statistics about stored mappings"""
        
        total_mappings = len(self.mappings)
        sources = {}
        exchanges = {}
        
        for mapping in self.mappings.values():
            sources[mapping.source] = sources.get(mapping.source, 0) + 1
            if mapping.exchange:
                exchanges[mapping.exchange] = exchanges.get(mapping.exchange, 0) + 1
        
        return {
            'total_mappings': total_mappings,
            'sources': sources,
            'exchanges': exchanges,
            'storage_file': str(self.storage_file),
            'last_updated': datetime.now().isoformat()
        }


# Global instance
ticker_mapping_manager = TickerMappingManager()
