#!/usr/bin/env python3
"""
Test script for web search fallback functionality
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_web_search_fallback():
    """Test the web search fallback functionality"""
    
    try:
        from src.utils.web_search_fallback import web_search_fallback
        
        print("🔍 Testing Web Search Fallback System")
        print("=" * 50)
        
        # Test cases
        test_cases = [
            {
                "company_name": "Apple Inc",
                "ticker": "AAPL",
                "query_type": "stock_price",
                "specific_request": "current stock price for Apple"
            },
            {
                "company_name": "Microsoft Corporation", 
                "ticker": "MSFT",
                "query_type": "company_fundamentals",
                "specific_request": "company fundamentals for Microsoft"
            },
            {
                "company_name": "Tesla Inc",
                "ticker": "TSLA", 
                "query_type": "financial_statements",
                "specific_request": "financial statements for Tesla"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📊 Test Case {i}: {test_case['company_name']} ({test_case['ticker']})")
            print(f"Query Type: {test_case['query_type']}")
            print("-" * 40)
            
            try:
                result = await web_search_fallback.search_financial_data(
                    company_name=test_case["company_name"],
                    ticker=test_case["ticker"],
                    query_type=test_case["query_type"],
                    specific_request=test_case["specific_request"]
                )
                
                if result.get("success"):
                    print(f"✅ Success! Source: {result.get('source', 'unknown')}")
                    
                    data = result.get("data", {})
                    if data.get("structured_data"):
                        print(f"📈 Structured Data: {data['structured_data']}")
                    
                    if data.get("key_findings"):
                        print(f"🔍 Key Findings: {len(data['key_findings'])} items")
                        for finding in data["key_findings"][:2]:  # Show first 2
                            print(f"   • {finding}")
                    
                    if data.get("sources_scraped"):
                        print(f"🌐 Sources Scraped: {data['sources_scraped']}")
                        
                    if data.get("message"):
                        print(f"💬 Message: {data['message']}")
                        
                else:
                    print(f"❌ Failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"💥 Exception: {str(e)}")
        
        # Test ticker discovery
        print(f"\n🔍 Testing Ticker Discovery")
        print("-" * 40)
        
        try:
            ticker = await web_search_fallback.find_missing_ticker("Amazon")
            if ticker:
                print(f"✅ Found ticker for Amazon: {ticker}")
            else:
                print("❌ Could not find ticker for Amazon")
        except Exception as e:
            print(f"💥 Ticker discovery failed: {str(e)}")
        
        print(f"\n🎯 Testing Enhanced Web Search Tool")
        print("-" * 40)
        
        try:
            from src.tools.web_search_tool import enhanced_web_search
            
            result = await enhanced_web_search.search_and_analyze(
                query="Apple AAPL stock price",
                max_results=3,
                include_analysis=True
            )
            
            if result.get("success"):
                print(f"✅ Enhanced search successful!")
                print(f"📊 Quality Score: {result.get('data_quality_score', 0)}")
                print(f"🌐 Sources Used: {result.get('sources_used', 0)}")
                print(f"📈 Successful Extractions: {result.get('successful_extractions', 0)}")
                
                if result.get("financial_metrics"):
                    print(f"💰 Financial Metrics: {result['financial_metrics']}")
                    
                if result.get("key_insights"):
                    print(f"🔍 Key Insights: {len(result['key_insights'])} items")
                    
            else:
                print(f"❌ Enhanced search failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"💥 Enhanced search exception: {str(e)}")
        
        print(f"\n✨ Web Search Fallback Test Complete!")
        
    except ImportError as e:
        print(f"❌ Import Error: {str(e)}")
        print("Make sure you're running this from the project root directory")
    except Exception as e:
        print(f"💥 Unexpected error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_web_search_fallback())
